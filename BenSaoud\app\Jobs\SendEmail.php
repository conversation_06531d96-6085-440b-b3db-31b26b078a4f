<?php

namespace App\Jobs;

use App\Mail\sendSubscription;
use App\Models\Transaction;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendEmail implements ShouldQueue
{
    use Queueable;
    public $tries = 3; // Number of retry attempts
    public $backoff = 60; //1min before retrying

    protected Transaction $transaction;

    public function __construct( Transaction $transaction)
    {
        $this->transaction = $transaction;

    }

    public function handle(): void
    {
        try {
            Mail::to($this->transaction->email)->send(new sendSubscription($this->transaction->Subscription->email, $this->transaction->Subscription->password));
            Log::info('Email sent successfully', ['email' => $this->transaction->email]);
            
            //Update transaction table after the email is sent
            Transaction::where('uuid', $this->transaction->uuid)->update(['email_send' => 1]);
        } catch (\Exception $e) {
            Log::error('Email sending failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
