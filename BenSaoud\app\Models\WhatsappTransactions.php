<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WhatsappTransactions extends Model
{
    use HasFactory; 
     

    protected $table = 'whatsappTransactions';
    protected $fillable = ['customer_id', 'order_id', 'amount', 'status', 'payment_provider', 'uuid', 'external_ref'];

    public function customer() { return $this->belongsTo(Customer::class); }

    public function order() { return $this->belongsTo(Orders::class); }

}