<?php

namespace App\Filament\Tutor\Pages;

use App\Models\Question;
use App\Models\Tutor;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class TutorQuestions extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document';
    protected static string $view = 'filament.pages.tutor-questions';
    protected static ?string $title = '';
    protected static bool $shouldRegisterNavigation = true;

    public function getTitle(): string
    {
        return __('filament-panels.question.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.question.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.question.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.question.title');
    }
    public function getViewData(): array
    {
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');
        $questions=Question::where('tutor_id', $tutorId)->latest();
        return [
            'allQuestions'       => (clone $questions)->get(),
            'answeredQuestions'  => (clone $questions)->whereNotNull('answer_text')->get(),
            'unansweredQuestions'=> (clone $questions)->whereNull('answer_text')->get(),
        ];
       
    }
}
