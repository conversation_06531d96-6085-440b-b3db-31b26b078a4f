<?php

namespace App\Enums;

enum WhatsAppMessageType: string
{
    case WELCOME = 'Welcome';
    case WELCOME_TRIAL = 'WelcomeTrial';
    case WELCOME_SUBSCRIPTION = 'WelcomeSubscription';
    case WELCOME_TUTOR = 'WelcomeTutor';


    public function label(): string
    {
        return match ($this) {
            self::WELCOME => 'Welcome',
            self::WELCOME_SUBSCRIPTION => 'Welcome Subscription',
            self::WELCOME_TRIAL => 'Welcome Trial',

        };
    }
}
