<?php

use App\Http\Controllers\ExportController;
use App\Http\Controllers\LanguageController;
use App\Models\Question;
use App\Models\TutorSubject;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

//For Locale
Route::get('/switch-lang/{locale}', [LanguageController::class, 'switchLang'])->name('switch-lang');

Route::get('/admin/reports/generate/{type}/{range}/{month?}', [ExportController::class, 'generate'])
->name('admin.reports.dynamic.pdf');
