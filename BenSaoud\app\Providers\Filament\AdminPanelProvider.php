<?php

namespace App\Providers\Filament;


use App\Filament\Pages\Settings;
use App\Filament\Widgets\OrdersChart;
use App\Filament\Widgets\PaymentMethodsPie;
use App\Filament\Widgets\TopCustomers;
use App\Filament\Widgets\TopSellingProducts;
use App\Filament\Widgets\TransactionStatusPie;
use App\Filament\Widgets\WhatsAppCustomerStats;
use App\Filament\Widgets\SalesPerformanceChart;
use App\Http\Middleware\SetLocale;
use CWSPS154\AppSettings\AppSettingsPlugin;
use CWSPS154\AppSettings\Models\AppSettings;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Pages;
use Filament\Panel;

use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Leandrocfe\FilamentApexCharts\FilamentApexChartsPlugin;
use Outerweb\FilamentSettings\Filament\Plugins\FilamentSettingsPlugin;
use Outerweb\Settings\Models\Setting;

/**
 * AdminPanelProvider
 * 
 * Configures the Filament admin panel for the WhatsApp Store application.
 * Handles panel customization, authentication, localization, and widget management.
 * Integrates with settings system for dynamic branding and configuration.
 */
class AdminPanelProvider extends PanelProvider
{
    /**
     * Configure the Filament admin panel
     * Sets up branding, authentication, resources, and middleware
     * 
     * @param Panel $panel The panel instance to configure
     * @return Panel The configured panel
     */
    public function panel(Panel $panel): Panel
    {
        // Initialize default values
        $locale = app()->getLocale();
        $appName = 'Whats App Store'; // Default app name
        $logoPath = null; // Default logo path

        // Check if the application is running in the console (during migrations)
        // and if the 'settings' table already exists before trying to fetch settings from DB.
        if (! App::runningInConsole() && Schema::hasTable('settings')) {
            // Attempt to retrieve app name and logo path from settings
            // Provide a default fallback if settings are not found
            $appName = Setting::get("general.app_name_{$locale}", 'Whats App Store');
            $logoPath = Setting::get('general.logo');
        }

        return $panel
            // Configure branding
            ->brandName($appName)
            ->brandLogo($logoPath ? asset('storage/' . $logoPath) : null)
            ->brandLogoHeight('4rem')
            ->id('admin')
            ->default() 
            ->path('store-admin')
            
            // Configure plugins
            ->plugins([
                // Settings plugin for managing application settings
                FilamentSettingsPlugin::make()
                    ->pages([
                        Settings::class,
                    ]),
                // ApexCharts plugin for advanced charting capabilities
                FilamentApexChartsPlugin::make()
            ])
            
            // Configure authentication
            ->login()
            
            // Configure user menu
            ->userMenuItems([
                // Language switcher menu item
                MenuItem::make()
                    ->label(fn(): string => app()->getLocale() === 'ar' ? 'English' : 'العربية')
                    ->url(fn(): string => '/switch-lang/' . (app()->getLocale() === 'ar' ? 'en' : 'ar'))
                    ->icon('heroicon-m-language'),
            ])
            
            // Configure theme colors
            ->colors([
                'primary' => Color::Amber,
            ])
            
            // Auto-discover resources and pages
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            
            // Configure widgets
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\StatsOverviewWidget::class,
                OrdersChart::class,
                SalesPerformanceChart::class,
                TransactionStatusPie::class,
                PaymentMethodsPie::class,
                WhatsAppCustomerStats::class,
                TopSellingProducts::class,
                TopCustomers::class,
            ])
            
            // Configure middleware stack
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SetLocale::class,
            ])
            
            // Configure authentication middleware
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
