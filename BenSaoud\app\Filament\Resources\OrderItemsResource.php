<?php

namespace App\Filament\Resources;

use App\Enums\OrderStatus;
use App\Filament\Resources\OrderItemsResource\Pages;
use App\Models\OrderItems;
use App\Services\StoreWhatsAppService;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class OrderItemsResource extends Resource
{
    // Defines the Eloquent model that this Filament resource manages.
    protected static ?string $model = OrderItems::class;

    // Sets the icon that will be displayed next to the resource in the Filament sidebar navigation.
    protected static ?string $navigationIcon = 'heroicon-c-shopping-bag';

    /**
     * Defines the form schema for creating and editing OrderItem records.
     *
     * @param Form $form
     * @return Form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Defines a section within the form to group related fields.
                Section::make()
                    ->schema([
                        // Text input for the product name, read-only as it comes from a relationship.
                        TextInput::make('product.name')
                            ->label('اسم المنتج')
                            ->readOnly(), // Makes the field non-editable

                        // Numeric input for the quantity of the item.
                        TextInput::make('quantity')
                            ->minValue(1) // Ensures quantity is at least 1
                            ->label('الكمية') // Label in Arabic: "Quantity"
                            ->numeric() // Ensures only numeric input
                            ->required(), // Makes the field mandatory

                        // Numeric input for the price of the item.
                        TextInput::make('price')
                            ->minValue(1) // Ensures price is at least 1
                            ->label('السعر') // Label in Arabic: "Price"
                            ->numeric() // Ensures only numeric input
                            ->required(), // Makes the field mandatory

                        // Select dropdown for the status of the order item.
                        Select::make('status')
                            ->label('الحالة') // Label in Arabic: "Status"
                            ->options([ // Defines the available options for the status
                                'pending' => 'قيد الانتظار', // "Pending"
                                'accepted' => 'مقبول', // "Accepted"
                                'rejected' => 'مرفوض', // "Rejected"
                            ])
                            ->required(), // Makes the field mandatory
                    ]),
            ]);
    }

    /**
     * Defines the table schema for listing OrderItem records.
     *
     * @param Table $table
     * @return Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Text column to display the 'id' of the order item.
                TextColumn::make('id')
                    ->label('#') 
                    ->sortable(), 

                // Text column to display the 'order_id' of the order item.
                TextColumn::make('order_id')
                    ->label(__('filament-panels.ordersItems.fields.orderId')) 
                    ->sortable(),
                // Text column to display the product name from the 'product' relationship.
                TextColumn::make('product.name')
                    ->searchable() 
                    ->label(__('filament-panels.ordersItems.fields.productName')), 

                // Text column to display the customer name from the 'order.customer' relationship.
                TextColumn::make('order.customer.name')
                    ->searchable() // Enables searching by customer name
                    ->label(__('filament-panels.orders.fields.name')),

                // Text column to display the customer's WhatsApp number from the 'order.customer' relationship.
                TextColumn::make('order.customer.whatsapp_number')
                    ->searchable() // Enables searching by WhatsApp number
                    ->label(__('filament-panels.orders.fields.phone')), 

                // Text column to display the quantity of the order item.
                TextColumn::make('quantity')
                    ->label(__('filament-panels.ordersItems.fields.quantity')), 

                // Text column to display the price of the order item, formatted as currency.
                TextColumn::make('price')
                    ->label(__('filament-panels.ordersItems.fields.price')) 
                    ->formatStateUsing(fn($state) => number_format($state, 2) . ' LYD'), // Formats price with 2 decimal places and "LYD" suffix

                // Text column to display the status of the order item as a badge with different colors.
                TextColumn::make('status')
                    ->label(__('filament-panels.ordersItems.fields.status')) // Label, translated
                    ->badge() // Displays the status as a badge
                    ->color(fn($record) => match ($record->status) { // Sets badge color based on status
                        OrderStatus::PENDING->value => 'warning', // Yellow for pending
                        OrderStatus::ACCEPTED->value => 'success', // Green for accepted
                        OrderStatus::REJECTED->value => 'danger', // Red for rejected
                    }),

                // Text column to display the creation timestamp.
                TextColumn::make('created_at')
                    ->dateTime() // Formats as date and time
                    ->label(__('filament-panels.products.fields.created_at')), 

                // Text column to display the last update timestamp.
                TextColumn::make('updated_at')
                    ->dateTime() // Formats as date and time
                    ->label(__('filament-panels.products.fields.updated_at')), 
            ])
            ->filters([
                // Filter for 'created_at' date range.
                Filter::make('created_at')
                    ->form([
                        // Date picker for the start date of creation.
                        DatePicker::make('created_from'),
                        // You could add another DatePicker for 'created_to' here for a range.
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'], // Apply filter only if 'created_from' is provided
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date), // Filter records created on or after the selected date
                            );
                    })
            ])
            ->actions([
                // Custom action to accept an order item.
                Tables\Actions\Action::make('قبول') 
                    ->label('قبول')
                    ->icon('heroicon-o-check') // Icon for acceptance
                    ->color('success') // Green button
                    ->visible(fn($record) => $record->status === OrderStatus::PENDING->value) // Only visible if status is pending
                    ->action(
                     fn($record) => app(StoreWhatsAppService::class)->approveOrderItem('approve_orderItem_' . $record->id)
                    ),

               Tables\Actions\Action::make('رفض') // Action label in Arabic: "Reject"
                    ->label('رفض')
                    ->icon('heroicon-o-x-mark') // Icon for rejection
                    ->color('danger') // Red button
                    ->visible(fn($record) => $record->status === OrderStatus::PENDING->value) // Only visible if status is pending
                    ->action(
                       fn($record) => app(StoreWhatsAppService::class)->rejectOrderItem('reject_orderItem_' . $record->id)
                    ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * Get the singular label for the model associated with this resource.
     *
     * @return string
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.ordersItems.singular');
    }

    /**
     * Get the plural label for the model associated with this resource.
     *
     * @return string
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.ordersItems.plural');
    }

    /**
     * Get the navigation label for this resource in the Filament sidebar.
     *
     * @return string
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.ordersItems.title');
    }

    /**
     * Defines the relationships that can be managed directly from this resource.
     *
     * @return array
     */
    public static function getRelations(): array
    {
        return [
        ];
    }

    /**
     * Defines the pages associated with this resource.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrderItems::route('/'), // Page to list all order items
            'create' => Pages\CreateOrderItems::route('/create'), // Page to create a new order item
            'edit' => Pages\EditOrderItems::route('/{record}/edit'), // Page to edit an existing order item
        ];
    }
}
