<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Log;

class Reports extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.admin.pages.reports';
    protected static ?string $title = 'تقارير النظام';

    public string $reportType = 'tutors';
    public string $range = 'week';
    public string $selectedMonth = '';
    public array $availableMonths = [];
    public string $pdfUrl = '';
    protected static bool $isLazy = false;
    protected static bool $hasDynamicProperties = true; 

    public function mount()
    {
        if ($this->range === 'month') {
            $this->generateAvailableMonths();
        }
    }

    public function updateRange($value)
    {
        $this->range = $value;
    
        if ($value === 'month') {
            $this->generateAvailableMonths();
        } else {
            $this->selectedMonth = '';
            $this->availableMonths = [];
        }
    }
    
    public function generateAvailableMonths()
    {
        $currentMonth = now()->month; 
        $this->availableMonths = [];
    
        for ($i = 1; $i <= $currentMonth; $i++) {
            $this->availableMonths[$i] = now()->startOfYear()->addMonths($i - 1)->translatedFormat('F');
        }
    }
    

    public function generateReport()
    {
        $this->pdfUrl = route('admin.reports.dynamic.pdf', [
            'type' => $this->reportType,
            'range' => $this->range,
            'month' => $this->range === 'month' ? $this->selectedMonth : null,
        ]);
    }

    public function getRangeLabel(): string
    {
        return match ($this->range) {
            'week' => 'الأسبوع',
            'year' => 'السنة',
            default => 'الشهر',
        };
    }
}
