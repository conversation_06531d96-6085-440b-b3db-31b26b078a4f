<?php

namespace App\Services;

use App\Enums\OrderStatus;
use App\Models\Categories;
use App\Models\Customer;
use App\Models\OrderItems;
use App\Models\Orders;
use App\Models\Products;
use App\Models\StoreBranch;
use App\Models\UserProductSessions;
use App\Services\WhatsAppService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Outerweb\Settings\Models\Setting;

class StoreWhatsAppService
{

    protected $whatsapp;

    public function __construct()
    {
        $this->whatsapp = new WhatsAppService();
    }

    /**
     * Handle WhatsApp Store Webhook Interactions.
     *
     * This function is triggered by webhook events from the Meta WhatsApp API.
     * 
     * 1. Processes order type messages by saving order details.
     * 2. Responds to new messages with a welcome list.
     * 3. Routes interactive replies to the appropriate handlers.
     *
     * @param array $value The webhook payload from WhatsApp.
     */
    public function whatsAppStoreWeebhook($value)
    {
        //  Check if the payload contains a message
        if (!isset($value['messages']) || empty($value['messages'])) {
            return;
        }
        //  Extract the values from the payload
        $message = $value['messages'][0];
        $from = $message['from'];
        $type = $message['type'] ?? null;
        $name = $value['contacts'][0]['profile']['name'] ?? 'عميل واتساب';
        $interactive = $message['interactive'] ?? null;
        $selection  = $interactive['list_reply']['id'] ?? $interactive['button_reply']['id'] ?? null;
        //  Handle WhatsApp order type messages
        if ($type === 'order') {
            return  $this->handleOrderMessage($message, $from, $name);
        }

        // Restart the flow if the user sends a new message (not an interactive selection)
        if (!$interactive) {
            return $this->sendStoreWelcomeMessage($message['from']);
        }
        //  Route interactive selection to the appropriate handler
        return match (true) {
            $selection === 'store_info' => $this->storeInfo($from),
            $selection === 'view_and_order' => $this->sendViewAndOrder($from),
            $selection === 'view_by_category' => $this->sendCategories($from, $name),
            str_starts_with($selection, 'cat_') => $this->sendProductsForCategory($from, $selection, $name),
            str_starts_with($selection, 'show_more_products_cat_') => $this->sendProductsForCategory($from, $selection, $name),
            $selection === 'show_catalog' => $this->sendCatelog($from),
            $selection === 'pay_cash' => $this->handleCashPayment($from),
            $selection === 'pay_online' => $this->handleOnlinePayment($from),
            $selection === 'my_orders' => $this->sendOrderList($from, $name),
            str_starts_with($selection, 'order_') => $this->sendOrderDetails($from, $selection),
            str_starts_with($selection, 'approve_orderItem_') => $this->approveOrderItem($selection),
            str_starts_with($selection, 'reject_orderItem_') => $this->rejectOrderItem($selection),
            $selection === 'view_pending_orders' => $this->viewPendingOrders($from),
            str_starts_with($selection, 'view_orderItem_') => $this->viewPendingOrdersDetails($selection),
            default => null
        };
    }
    /**
     * Sends the initial welcome message to the user on WhatsApp.
     *
     * This message includes a list of interactive options:
     * 1- Store Info
     * 2- Browse and Order Products
     * 3- View My Orders
     *
     * @param string $to The recipient WhatsApp phone number.
     * 
     */
    private function sendStoreWelcomeMessage(string $to): void
    {
        $welcomeMessage=Setting::get('general.welcome_message')??"*مرحباً بك! يرجى اختيار أحد الخيارات من القائمة:*";
        try {
            $sections = [
                [
                    'title' => 'القائمة الرئيسية',
                    'rows' => [
                        [
                            'id' => 'store_info',
                            'title' => 'معلومات المتجر'
                        ],
                        [
                            'id' => 'view_and_order',
                            'title' => 'عرض وطلب المنتجات'
                        ],
                        [
                            'id' => 'my_orders',
                            'title' => 'طلباتي'
                        ]
                    ]
                ]
            ];
            $this->whatsapp->sendList($to, $welcomeMessage, "عرض القائمة", $sections);
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Sends store branches information to the user via WhatsApp.
     *
     * If no branches exist, a fallback message is sent.
     * Otherwise, it formats and sends each branch's name, address, and phone number.
     *
     * @param string $to The recipient's WhatsApp number.
     * @return void
     */
    private function storeInfo(string $to): void
    {
        try {
            $storeInfo = StoreBranch::all();
            if ($storeInfo->isEmpty()) {
                $message = " لا توجد معلومات حاليًا.";
            } else {
                $message = "📍 *فروع المتجر ومعلومات التواصل:*\n\n";

                foreach ($storeInfo as $branch) {
                    $message .= " *{$branch->name}*\n";
                    $message .= "📍 العنوان: {$branch->address}\n";
                    $message .= "📞 الهاتف: {$branch->phone}\n\n";
                }
            }
            $this->whatsapp->sendText($to, $message);
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Sends interactive buttons to the user to choose how to browse products.
     *
     * The user can choose to browse by category or view the full product catalog.
     *
     * @param string $to The recipient's WhatsApp number.
     * @return void
     */
    private function sendViewAndOrder(string $to): void
    {
        try {
            $this->whatsapp->sendButtons($to, "*مرحبًا بك! كيف تود استعراض منتجاتنا؟*", [
                [
                    "type" => "reply",
                    "reply" => [
                        "id" => "view_by_category",
                        "title" => "حسب الأقسام"
                    ]
                ],

                [
                    "type" => "reply",
                    "reply" => [
                        "id" => "show_catalog",
                        "title" => "عرض الكتالوج"
                    ]
                ]
            ]);
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Sends a list of product categories to the user via WhatsApp.
     *
     * If categories exist, resets the user session and sends them as a list.
     * If no categories are found, send the user that no categories are available.
     *
     * @param string $to   The WhatsApp number of the recipient.
     * @param string $name The user's name (used for session handling).
     * @return void
     */
    private function sendCategories(string $to, string $name): void
    {
        try {
            // Fetch subscription plans from the database
            $categories = Categories::has('products')->get();
            if ($categories->isEmpty()) {
                $this->whatsapp->sendText($to, ' لا توجد أقسام . يرجى المحاولة لاحقاً.');
            } else {
                $this->resetCategorySession($to, $name);
                // Call the function to send the message
                $this->sendCategoryList($to, $categories);
            }
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }


    /**
     * Sends a list of available product categories to the user via WhatsApp.
     *
     * @param string $to  The recipient's WhatsApp number.
     * @param $categories  The list of category models.
     * @return void
     */
    private function sendCategoryList(string $to, $categories): void
    {
        try {
            $chunks = $categories->chunk(10);
            $part = 1;

            foreach ($chunks as $chunk) {
                $rows = [];

                foreach ($chunk as $category) {
                    $rows[] = [
                        "id" => "cat_" . $category->id,
                        "title" => $category->name,
                    ];
                }

                $this->whatsapp->sendList(
                    $to,
                    "*اختر القسم الذي تريد استعراضه* (جزء {$part}):",
                    "اختر القسم",
                    [
                        [
                            "title" => "الأقسام - جزء {$part}",
                            "rows" => $rows
                        ]
                    ]
                );

                $part++;
            }
        } catch (\Exception $error) {
            Log::error('Error sending category list: ' . $error->getMessage());
        }
    }
    /**
     * Sends a WhatsApp catalog template message to the user.
     *
     * This message includes a View Catalog button linked to a specific product retailer ID.
     *
     * @param string $to  The recipient's WhatsApp number.
     * @return void
     */
    private function sendCatelog(string $to): void
    {
        try {
            $this->whatsapp->sendTemplate(
                $to,
                'catelog_v1',
                'ar',
                [
                    [
                        "type" => "button",
                        "sub_type" => "CATALOG",
                        "index" => 0,
                        "parameters" => [
                            [
                                "type" => "action",
                                "action" => [
                                    "thumbnail_product_retailer_id" => "yb2m46clim"
                                ]
                            ]
                        ]
                    ]
                ],
                'WhatsAppStore'
            );
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Sends a carousel of products to the user for a selected category.
     * Handles via session and shows View More if more products exist.
     *
     * @param string $to The user's WhatsApp number.
     * @param string $selection  The selected category or show more ID.
     * @param string $name The user's name for customer creation.
     * @return void
     */
    private function sendProductsForCategory(string $to, string $selection, string $name): void
    {
        try {
            $catalog_id = config('api.catelog_id');
            //Extract the actual category ID 
            $categoryId = str_replace(['show_more_products_cat_', 'cat_'], '', $selection);

            //  Get or create customer by phone number
            $customerId = (int) $this->getOrCreateCustomer($to, $name);
            Log::info("customer" . $customerId);

            //  Get last session for this user/category
            $session = UserProductSessions::where('customer_id', $customerId)
                ->where('session_type', 'category')
                ->where('reference_id', $categoryId)
                ->first();

            $lastProductId = $session?->last_product_id;
            Log::info('lastProductId', [$lastProductId]);

            $productsQuery = Products::whereHas('categories', function ($q) use ($categoryId) {
                $q->where('id', $categoryId);
            })
                ->when($lastProductId, fn($q) => $q->where('id', '>', $lastProductId))
                ->orderBy('id');


            $products = $productsQuery->limit(10)->get();
            Log::info('pro', [$products]);
            $nextProductExists = $productsQuery->skip(10)->first();
            // If only one product is returned duplicate it to have two products
            if ($products->count() === 1) {
                $products->push($products->first());
            }
            $cards = [];
            // Build carousel cards
            foreach ($products as $index => $product) {
                $cards[] = [
                    "card_index" => $index,
                    "components" => [
                        [
                            "type" => "header",
                            "parameters" => [
                                [
                                    "type" => "product",
                                    "product" => [
                                        "product_retailer_id" => $product->id,
                                        "catalog_id" =>  $catalog_id
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];
            }
            // Send carousel template
            $this->whatsapp->sendTemplate(
                $to,
                'carousel_template_product_cards_v1',
                'ar',
                [
                    [
                        'type' => 'carousel',
                        'cards' => $cards
                    ]
                ],
                'WhatsAppStore'
            );

            //  If more products send Show More button
            if ($nextProductExists) {
                $this->viewMore($to, $categoryId);
                //  Save the last product ID in session
                $lastProduct = $products->last();
                UserProductSessions::updateOrCreate(
                    [
                        'customer_id' => $customerId,
                        'session_type' => 'category',
                        'reference_id' => $categoryId
                    ],
                    [
                        'last_product_id' => $lastProduct->id
                    ]
                );
            } else {
                $this->resetCategorySession($to, $name);
            }
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Sends a "View More" button to the user for a given category.
     *
     * @param string $to The user WhatsApp number.
     * @param $categoryId  The category ID to paginate.
     * @return void
     */
    private function viewMore(string $to, $categoryId): void
    {
        try {
            $buttons = [
                [
                    'type' => 'reply',
                    'reply' => [
                        'id' => 'show_more_products_cat_' . $categoryId,
                        'title' => '📥 عرض المزيد'
                    ]
                ]
            ];
            $this->whatsapp->sendButtons($to,  ' هل ترغب في عرض المزيد من المنتجات؟', $buttons);
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }


    /**
     * Handles an incoming WhatsApp order message.
     * 
     * 1- Extracts product details from the order message.
     * 2- Creates a new order and saves associated items.
     * 3- Calculates the total price.
     * 4- Asks the user to select a payment method.
     *
     * @param array $message  The message payload from WhatsApp.
     * @param string $from    The user's phone number.
     * @param string $name    The user's name.
     * @return void
     */
    private function handleOrderMessage(array $message, string $from, string $name): void
    {
        try {

            DB::transaction(function () use ($message, $from, $name) {
                $customerId = $this->getOrCreateCustomer($from, $name);
                //  Check if this customer has any pending approval items
                $hasPending = $this->getPendingItems($from, $name);

                if ($hasPending) {
                    $this->whatsapp->sendText($from, " لديك طلب سابق يحتوي على منتجات قيد المراجعة. الرجاء الانتظار حتى يتم تأكيدها قبل تقديم طلب جديد.");
                    Log::info("Customer $from tried to place new order but has pending approval items.");
                    return;
                }
                $order = $message['order'] ?? null;
                if (!$order || empty($order['product_items'])) {
                    Log::warning("Invalid or empty order received from: $from");
                    return;
                }

                $productItems = $order['product_items'] ?? [];
                Log::info("New order from: $from");

                $order = Orders::create([
                    'customer_id' => $customerId,
                    'payment_method' => null,
                    'total_price' => 0,
                ]);
                $totalPrice = 0;
                $pendingItems = [];
                $approvedItems = [];

                foreach ($productItems as $item) {
                    $id = $item['product_retailer_id'];
                    $quantity = (int)$item['quantity'];
                    $price = $item['item_price'];

                    $product = Products::where("id", $id)->lockForUpdate()->first();
                    if (!$product) {
                        Log::warning("Product not found for id: $id | Order ID: {$order->id}");
                        $this->whatsapp->sendText($from, "المعذرة, المنتج {$product->name} غير متوفر حاليا.");
                        throw new \Exception('Failed to find product.');
                    }
                    if ($product->quantity < $quantity) {
                        $this->whatsapp->sendText($from, "المعذرة, فقط {$product->quantity} من {$product->name} متوفر حاليا.");
                        throw new \Exception('Product order quntity is more then product quntity .');
                    }

                    Log::info("Product: $product->id | quantity: $quantity | price: $price ");
                    $status = $product->quantity < 10 ? OrderStatus::PENDING : OrderStatus::ACCEPTED;
                    Log::info("Product {$product->name} assigned status: " . $status->value);

                    $orderItem = OrderItems::create([
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'quantity' => $quantity,
                        'price' => $price,
                        "status" => $status
                    ]);
                    Log::info("orderItem:", [$orderItem]);

                    $itemSummary = "- {$product->name} ({$quantity}x) – {$price} × {$quantity} = " . ($price * $quantity) . " " . ($status == OrderStatus::PENDING ? "  بانتظار موافقة المتجر" : "");
                    // Only reduce product stock if approved immediately
                    if ($status === OrderStatus::ACCEPTED) {
                        $product->quantity -= $quantity;
                        $product->save();
                        $totalPrice += $price * $quantity;
                        $approvedItems[] = $itemSummary;
                    } else {
                        $pendingItems[] = $itemSummary;
                        $this->notifyStoreForApproval($orderItem);
                    }
                }

                $order->update([
                    'total_price' => $totalPrice
                ]);
                $messageLines = ["شكرًا لطلبك! ملخص الطلب:"];

                if ($approvedItems) {
                    $messageLines[] = "\n المؤكدة:";
                    $messageLines = array_merge($messageLines, $approvedItems);
                }

                if ($pendingItems) {
                    $messageLines[] = "\n🕒 المنتجات قيد الانتظار:";
                    $messageLines = array_merge($messageLines, $pendingItems);
                    $messageLines[] = "\nسنقوم بإبلاغك فورًا عند تأكيد المنتجات المعلقة.";
                }
                $this->whatsapp->sendText($from, implode("\n", $messageLines));

                if (empty($pendingItems)) {
                    $this->askPaymentMethod($from);
                }
            });
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Send a product approval request to the store or delivery person
     * with buttons: Approve, Reject, View All Pending
     * @param OrderItems $orderItem  
     * @return void
     */
    public function notifyStoreForApproval(OrderItems $orderItem): void
    {
        $product = $orderItem->product;
        $order = $orderItem->order;
        $customer = $order->customer;

        $storePhone = Setting::get('general.phone');
        if ($storePhone == null) {
            $message = " *ملاحظة: هذه الرسالة ترسل الى رقم المتجر للتحقق من المنتجات قبل الدفع.*\n";
            $message .= " *لكن نظرا لاننا في وضع الاختبار حاليا، تم إرسالها إليك للتجربة فقط.*\n\n";
            $message .= " طلب جديد يحتاج موافقتك:\n\n";
        } else {
            $message = " طلب جديد يحتاج موافقتك:\n\n";
        }
        $message .= "🔸 المنتج: {$product->name}\n";
        $message .= " الكمية: {$orderItem->quantity}\n";
        $message .= " السعر: {$orderItem->price} د.ل\n";
        $message .= " العميل: {$customer->name} \n";
        $message .= " رقم العميل: +{$customer->whatsapp_number}\n";
        $message .= "هل ترغب في قبول هذا المنتج؟";

        $to = $storePhone == "0" ? $customer->whatsapp_number : $storePhone;

        $this->whatsapp->sendButtons($to, $message, [
            [
                'type' => 'reply',
                'reply' => [
                    'id' => "approve_orderItem_{$orderItem->id}",
                    'title' => ' قبول',
                ],
            ],
            [
                'type' => 'reply',
                'reply' => [
                    'id' => "reject_orderItem_{$orderItem->id}",
                    'title' => ' رفض',
                ],
            ],
            [
                'type' => 'reply',
                'reply' => [
                    'id' => 'view_pending_orders',
                    'title' => ' عرض الطلبات المعلقة',
                ],
            ],
        ]);
    }
    /**
     * Handle approval of a pending order item.
     * Updates status to approved, reduces stock, and notifies the customer.
     * @param int $orderItemId 
     * @return void
     */
    public function approveOrderItem($selection): void
    {
        $orderItemId = str_replace('approve_orderItem_', '', $selection);

        $orderItem = OrderItems::with('product', 'order.customer')->find($orderItemId);
        $storePhone = Setting::get('general.phone');
        $storePhone = $storePhone == null ? $orderItem->order->customer->whatsapp_number : $storePhone;
        if (!$orderItem || $orderItem->status !== OrderStatus::PENDING->value) {
            Log::warning("Order item not found or already processed: $orderItemId ");
            return;
        }
        DB::transaction(function () use ($orderItem, $storePhone) {
            $product = $orderItem->product;

            if ($product->quantity < $orderItem->quantity) {
                // Not enough stock
                $this->whatsapp->sendText($storePhone, "عذرًا، لا يمكن تأكيد المنتج {$product->name} بسبب عدم توفر الكمية المطلوبة.");
                return;
            }

            // Update status and reduce stock
            $orderItem->status = OrderStatus::ACCEPTED;
            $orderItem->save();
            $this->recalculateTotal($orderItem);


            $product->quantity -= $orderItem->quantity;
            $product->save();

            // Notify customer
            $hasPending = $this->getPendingItems($orderItem->order->customer->whatsapp_number, $orderItem->order->customer->name);
            if (!$hasPending) {
                $this->whatsapp->sendText($orderItem->order->customer->whatsapp_number, "تم تأكيد المنتج: {$product->name}");
                $this->sendOrderDetails($orderItem->order->customer->whatsapp_number, $orderItem->order_id);
                $this->askPaymentMethod($orderItem->order->customer->whatsapp_number);
            } else {
                $this->whatsapp->sendText($orderItem->order->customer->whatsapp_number, "تم تأكيد المنتج: {$product->name} \n في انتطار قبول باقي المنتجات.");
            }
        });
    }

    /**
     * Handle rejection of a pending order item.
     * Updates status to rejected and notifies the customer.
     */
    public function rejectOrderItem($selection): void
    {
        $orderItemId = str_replace('reject_orderItem_', '', $selection);

        $orderItem = OrderItems::with('product', 'order.customer')->find($orderItemId);
        if (!$orderItem || $orderItem->status !== OrderStatus::PENDING->value) {
            Log::warning("Order item not found or already processed: $orderItemId");
            return;
        }

        $orderItem->status = OrderStatus::REJECTED;
        $orderItem->save();
        $this->recalculateTotal($orderItem);
        $productName = $orderItem->product->name;
        $this->whatsapp->sendText($orderItem->order->customer->whatsapp_number, "عذرًا، تم رفض المنتج: {$productName}  ولن يتم تضمينه في الطلب.");
        $hasPending = $this->getPendingItems($orderItem->order->customer->whatsapp_number, $orderItem->order->customer->name);
        $hasAccepted = OrderItems::where('order_id', $orderItem->order_id)
            ->where('status', OrderStatus::ACCEPTED)
            ->exists();
        if (!$hasPending && $hasAccepted) {
            $this->sendOrderDetails($orderItem->order->customer->whatsapp_number, $orderItem->order_id);
            $this->askPaymentMethod($orderItem->order->customer->whatsapp_number);
        }
    }

    /**
     * View all pending order items and send a summary to the store.
     */
    public function viewPendingOrders($to): void
    {
        // $storePhone = config('api.store_order_number');
        $storePhone = Setting::get('general.phone');
        $storePhone = $storePhone == null ? $to : $storePhone;

        $pendingItems = OrderItems::with('product', 'order.customer')
            ->where('status', OrderStatus::PENDING)
            ->get();

        if ($pendingItems->isEmpty()) {
            $this->whatsapp->sendText($storePhone, "لا توجد منتجات قيد الانتظار حاليا.");
            return;
        }

        $chunks = $pendingItems->chunk(10);
        $part = 1;

        foreach ($chunks as $chunk) {
            $rows = [];

            foreach ($chunk as $item) {
                $rows[] = [
                    'id' => "view_orderItem_{$item->id}",
                    'title' => $item->product->name,
                    'description' => "الكمية: {$item->quantity} - العميل: {$item->order->customer->whatsapp_number}",
                ];
            }

            $this->whatsapp->sendList(
                $storePhone,
                "*المنتجات قيد الانتظار* (جزء {$part})\nاختر منتجاً لمراجعته:",
                'عرض الطلبات',
                [
                    [
                        'title' => "طلبات قيد الموافقة - جزء {$part}",
                        'rows' => $rows,
                    ],
                ]
            );

            $part++;
        }
    }
    public function viewPendingOrdersDetails($selection): void
    {
        $orderItemId = str_replace('view_orderItem_', '', $selection);
        $orderItem = OrderItems::where("id", $orderItemId)->first();
        $this->notifyStoreForApproval($orderItem);
    }


    /**
     * Asks the user to choose a payment method after placing an order.
     *
     * @param string $to  The customer WhatsApp number.
     * @return void
     */
    private function askPaymentMethod(string $to): void
    {
        try {

            $buttons = [
                [
                    "type" => "reply",
                    "reply" => [
                        "id" => "pay_cash",
                        "title" => " الدفع عند الاستلام"
                    ]
                ],
                [
                    "type" => "reply",
                    "reply" => [
                        "id" => "pay_online",
                        "title" => " الدفع أونلاين"
                    ]
                ]
            ];

            $this->whatsapp->sendButtons($to,  " تم تاكيد طلبك!\nكيف تفضل طريقة الدفع؟", $buttons);
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }


    /**
     * Handles selection of Cash on Delivery by the user.
     * Updates the order and notifies delivery.
     *
     * @param string $to  The customer WhatsApp number.
     * @return void
     */
    private function handleCashPayment(string $to): void
    {
        try {
            $this->updatePaymentMethod($to, 'cash');
            $this->whatsapp->sendText($to, " تم اختيار الدفع عند الاستلام.\nسنتواصل معك قريبًا لتأكيد التوصيل.");
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Handles selection of Online Payment by the user.
     * Sends a payment link.
     *
     * @param string $to  The customer WhatsApp number.
     * @return void
     */
    private function handleOnlinePayment(string $to): void
    {
        try {
            $this->updatePaymentMethod($to, 'online payment');
            $paymentLink = "https://example.com/pay?phone=" . $to;
            $this->whatsapp->sendText($to,  " للدفع أونلاين، تفضل بالرابط التالي:\n$paymentLink");
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Notifies the delivery team with the order details via WhatsApp.
     *
     * @param  $order
     * @return void
     */
    private function notifyDelivery($order): void
    {
        try {


            $items = $order->orderItems()->with('product')->get();
            $storePhone = Setting::get('general.phone');
            Log::info('storePhone', [$storePhone]);


            if ($storePhone === "0") {
                $message = " *ملاحظة: هذه الرسالة ترسل الى رقم المتجر .*\n";
                $message .= " *لكن نظرا لاننا في وضع الاختبار حاليا، تم إرسالها إليك للتجربة فقط.*\n\n";
                $message .= "*طلب جديد من واتساب*\n\n";
            } else {
                $message = "*طلب جديد من واتساب*\n\n";
            }

            $message .= " *العميل:* {$order->customer->name}\n";
            $message .= " *رقم العميل:* +{$order->customer->whatsapp_number}\n";
            $message .= " *طريقة الدفع:* " . ($order->payment_method === 'cash' ? 'نقدا عند الاستلام' : 'دفع إلكتروني') . "\n";
            $message .= " *تفاصيل الطلب:*\n";

            foreach ($items as $item) {
                $message .= "- {$item->product->name} × {$item->quantity}\n";
            }
            $message .= "\n *الإجمالي:* {$order->total_price} د.ل";

            $to = $storePhone === "0" ? $order->customer->whatsapp_number : $storePhone;
            Log::info('storePhone', [$to]);

            $this->whatsapp->sendText($to, $message);
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
        }
    }

    /**
     * Retrieves an existing customer by phone or creates a new one.
     *
     * @param string $phone
     * @param string $name
     * @return Customer ID
     */
    public function getOrCreateCustomer(string $phone, string $name = 'عميل واتساب')
    {
        $customer = Customer::firstOrCreate(
            ['whatsapp_number' => $phone],
            ['name' => $name]
        );

        return $customer->id;
    }

    /**
     * Resets the product session for a specific customer.
     *
     * @param string $to
     * @param string $name
     * @return void
     */
    public function resetCategorySession(string $to, string $name): void
    {
        $customerId = $this->getOrCreateCustomer($to, $name);
        UserProductSessions::where('customer_id', $customerId)
            ->where('session_type', 'category')
            ->delete();
    }
    /**
     * Updates the payment method for the latest order of a customer and notifies delivery.
     *
     * @param string $to
     * @param string $type
     * @return void
     */
    public function updatePaymentMethod(string $to, string $type): void
    {
        $customerId = $this->getOrCreateCustomer($to,);
        $order = Orders::where('customer_id', $customerId)
            ->latest()
            ->first();

        if (!$order) return;
        $order->update([
            'payment_method' => $type,
        ]);
        $this->notifyDelivery($order);
    }
    /**
     * Recalculate the order total.
     *
     * @param orderItem 
     * @return void
     */
    public function recalculateTotal($orderItem): void
    {
        $total = OrderItems::where('order_id', $orderItem->order_id)
            ->where('status', OrderStatus::ACCEPTED)
            ->sum(DB::raw('quantity * price'));

        $orderItem->order->update([
            'total_price' => $total
        ]);
    }

    /*
        Get pending order Items
        */
    private function getPendingItems($to, $name)
    {
        $customerId = $this->getOrCreateCustomer($to, $name);

        //  Check if this customer has any pending approval items
        $hasPending = Orders::where('customer_id', $customerId)
            ->whereHas('orderItems', function ($query) {
                $query->where('status', OrderStatus::PENDING);
            })
            ->exists();
        return $hasPending;
    }
    /**
     * Sends a list of previous orders for the customer as a WhatsApp interactive list.
     *
     * @param string $to
     * @param string $name
     * @return void
     */
    private function sendOrderList(string $to, string $name): void
    {
        try {
            $customerId = $this->getOrCreateCustomer($to, $name);

            $orders = Orders::where('customer_id', $customerId)
                ->orderBy('created_at', 'desc')
                ->get();

            if ($orders->isEmpty()) {
                $this->whatsapp->sendText($to, ' لا توجد طلبات حالياً.');
                return;
            }

            $orderChunks = $orders->chunk(10);

            $page = 1;

            foreach ($orderChunks as $chunk) {
                $rows = [];

                foreach ($chunk as $order) {
                    $rows[] = [
                        'id' => 'order_' . $order->id,
                        'title' => 'طلب رقم #' . $order->id,
                        'description' => 'الإجمالي: ' . $order->total_price . ' د.ل - ' . $order->created_at->format('Y-m-d')
                    ];
                }

                $sections = [
                    [
                        'title' => 'طلباتك (جزء ' . $page . ')',
                        'rows' => $rows
                    ]
                ];

                $this->whatsapp->sendList(
                    $to,
                    "قائمة طلباتك\nالجزء $page:",
                    'عرض الطلبات',
                    $sections
                );

                $page++;
            }
        } catch (\Exception $error) {
            Log::error('Error in sendOrderList: ' . $error->getMessage());
        }
    }
    /**
     * Sends detailed info of a specific order to the customer.
     *
     * @param string $to
     * @param string $orderId
     */
    private function sendOrderDetails(string $to, string $orderId)
    {
        $orderId = str_replace('order_', '', $orderId);
        $order = Orders::with(['orderItems.product', 'customer'])->find($orderId);
        if (!$order) {
            return $this->whatsapp->sendText($to, 'الطلب غير موجود.');
        }

        $message = " *تفاصيل الطلب #{$order->id}:*\n";
        foreach ($order->orderItems as $item) {
            $statusText = match ($item->status) {
                OrderStatus::REJECTED->value => " ( مرفوض )",
                OrderStatus::PENDING->value  => "  ( قيد المراجعة )",
                OrderStatus::ACCEPTED->value => "",
                default => "غير معروف"
            };

            $total = $item->price * $item->quantity;
            if ($item->status == OrderStatus::REJECTED->value) {
                $message .= "- {$item->product->name} - ({$statusText})\n";
            } else {
                $message .= "- {$item->product->name} × {$item->quantity} = {$total} د.ل {$statusText}\n";
            }
        }
        if ($order->payment_method === 'cash') {
            $message .= "\n *طريقة الدفع:* " .  'نقدًا عند الاستلام';
        } elseif ($order->payment_method === 'online payment') {
            $message .= "\n *طريقة الدفع:* " . 'دفع إلكتروني';
        } else {
            $message .= "\n *طريقة الدفع:* " . 'لم يتم اختيار طريقة الدفع ';
        }
        $message .= "\n *الإجمالي:* {$order->total_price} د.ل";
        $message .= "\n *التاريخ:* " . $order->created_at->format('Y-m-d H:i');

        $this->whatsapp->sendText($to, $message);
    }
}
