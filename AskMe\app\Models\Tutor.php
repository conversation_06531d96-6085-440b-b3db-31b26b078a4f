<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tutor extends Model
{

    protected $fillable = ['name', 'phone', 'rate_per_question', 'status','user_id'];
    protected $casts = [
        'id' => 'integer',
        'rate_per_question' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function questions(): HasMany
    {
        return $this->hasMany(Question::class);
    }
    public function tutorSubjects(): HasMany
    {
        return $this->hasMany(TutorSubject::class, 'tutor_id');
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function tutorActivites(): HasMany
    {
        return $this->hasMany(TutorActivity::class, 'tutor_id');
    }
    
}
