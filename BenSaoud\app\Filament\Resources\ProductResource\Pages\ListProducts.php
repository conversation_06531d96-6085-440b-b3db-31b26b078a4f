<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use App\Models\ProductImages;
use App\Models\Products;
use App\Services\CatalogSyncService;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * ListProducts
 * 
 * Page component for listing and managing products in the Filament admin panel.
 * Provides functionality for creating products and bulk importing products via Excel/CSV files.
 * Handles product creation with image management and catalog synchronization.
 */
class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;

    /**
     * Define the header actions available on this page
     * Includes create action and file upload for bulk product import
     * 
     * @return array Array of header actions
     */
    protected function getHeaderActions(): array
    {
        return [
            // Standard create action for individual products
            Actions\CreateAction::make(),
            
            // Custom action for bulk product import
            Action::make('uploadFile')
                ->label('Import Products')
                ->icon('heroicon-o-arrow-up-tray')
                ->form([
                    // File upload field with supported formats
                    FileUpload::make('file')
                        ->label('Excel or CSV File')
                        ->directory('product_imports') // stored in storage/app/public/product_imports
                        ->required()
                        ->acceptedFileTypes([
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            'text/csv',
                        ]),
                ])
                ->action(function (array $data) {
                    $relativePath = $data['file'];
                    try {
                        // Process the uploaded file
                        $this->processProductImport($relativePath);

                        // Show success notification
                        Notification::make()
                            ->title('Products Imported')
                            ->success()
                            ->body('File processed successfully.')
                            ->send();
                    } catch (\Exception $e) {
                        // Log and show error notification
                        Log::error('Import error: ' . $e->getMessage());

                        Notification::make()
                            ->title('Import Failed')
                            ->danger()
                            ->body('There was a problem processing your file.')
                            ->send();
                    }
                })
                ->modalHeading('Upload Product File')
                ->modalButton('Upload'),
        ];
    }

    /**
     * Process the imported product file
     * Handles both CSV and Excel formats, creates products with images,
     * and synchronizes with the catalog service
     * 
     * @param string $filePath Path to the uploaded file
     * @throws \Exception If product creation or catalog sync fails
     */
    protected function processProductImport(string $filePath): void
    {
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        $fullPath = storage_path('app/public/' . $filePath);

        $rows = [];

        // Parse file based on extension
        if ($extension === 'csv') {
            // Process CSV file using native parser
            if (($handle = fopen($fullPath, 'r')) !== false) {
                while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                    $rows[] = $data;
                }
                fclose($handle);
            }
        } else {
            // Process Excel file using PhpSpreadsheet
            $spreadsheet = IOFactory::load($fullPath);
            $rows = $spreadsheet->getActiveSheet()->toArray();
        }

        // Process each row (skip header)
        foreach (array_slice($rows, 1) as $row) {
            DB::transaction(function () use ($row) {
                // Extract row data
                [$name, $retailer_id, $price, $quantity, $image_urls, $description, $url, $brand] = $row;
                
                // Create the product record
                $product = Products::create([
                    'code' => $retailer_id,
                    'name' => $name,
                    'price' => $price,
                    'quantity' => $quantity,
                    'desc' => $description,
                    'brand' => $brand,
                    'website_link' => $url,
                ]);

                if (!$product) {
                    throw new \Exception('Failed to add product.');
                }

                // Process product images
                $images = collect(explode(',', $image_urls))
                    ->map(fn($url) => trim($url))   // remove extra spaces
                    ->filter()                       // remove empty values
                    ->values();

                // Create image records for the product
                foreach ($images as $index => $url) {
                    ProductImages::create([
                        'product_id' => $product->id,
                        'image_url' => $url,
                    ]);
                }

                // Sync product with Meta Catalog
                $response = app(CatalogSyncService::class)->createProduct($product);
                $responseJson = $response->json();

                // Handle catalog sync errors
                if (isset($responseJson['error'])) {
                    Log::error('Meta Catalog API Error:', $responseJson);
                    throw new \Exception($responseJson['error']['message'] ?? 'Failed to add product.');
                }

                // Update product with catalog ID
                $product->update(['product_catelog_id' => $responseJson['id']]);

                return $product;
            });
        }
    }
}
