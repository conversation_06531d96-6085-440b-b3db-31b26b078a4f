<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StoreBranchResource\Pages;
use App\Models\StoreBranch;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

/**
 * StoreBranchResource
 * 
 * This resource manages store branches in the Filament admin panel.
 * It provides CRUD operations for store branches with fields for name, address, and phone.
 */
class StoreBranchResource extends Resource
{
    protected static ?string $model = StoreBranch::class;

    protected static ?string $navigationIcon = 'heroicon-m-building-office-2';

    /**
     * Define the form fields for creating and editing store branches
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Required name field for the store branch
                TextInput::make('name')->required()
                    ->label(__("filament-panels.storeBranch.fields.name")),
                // Address field as a textarea for longer addresses
                Textarea::make('address')
                    ->label(__("filament-panels.storeBranch.fields.address")),
                // Phone field with telephone input type
                TextInput::make('phone')->tel()
                    ->label(__("filament-panels.storeBranch.fields.phone")),
            ]);
    }

    /**
     * Define the table columns and actions for listing store branches
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Searchable columns for name, address, and phone
                TextColumn::make('name')->searchable()
                    ->label(__("filament-panels.storeBranch.fields.name")),
                TextColumn::make('address')->searchable()
                    ->label(__("filament-panels.storeBranch.fields.address")),
                TextColumn::make('phone')->searchable()
                    ->label(__("filament-panels.storeBranch.fields.phone")),
            ])
            ->filters([
                // No filters defined 
            ])
            ->actions([
                // Individual record actions
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                // Bulk actions for multiple records
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * Define any relationships to be displayed in the resource
     */
    public static function getRelations(): array
    {
        return [
            // No relationships  
        ];
    }

    /**
     * Get the singular label for the model
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.storeBranch.singular');
    }

    /**
     * Get the plural label for the model
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.storeBranch.plural');
    }

    /**
     * Get the navigation label for this resource
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.storeBranch.title');
    }

    /**
     * Define the available pages for this resource
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStoreBranches::route('/'),
            'create' => Pages\CreateStoreBranch::route('/create'),
            'edit' => Pages\EditStoreBranch::route('/{record}/edit'),
        ];
    }
}
