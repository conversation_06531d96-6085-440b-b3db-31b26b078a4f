<?php

namespace App\Filament\Resources\DiscountProductsResource\RelationManagers;

use App\Jobs\SyncDiscountProductToCatalogJob;
use App\Models\Products;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\AttachAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

/**
 * ProductsRelationManager
 * 
 * Manages the relationship between discounts and products in the Filament admin panel.
 * Handles attaching products to discounts and automatically syncs discounted products
 * to the catalog when appropriate. Includes functionality for both individual and bulk
 * product management.
 */
class ProductsRelationManager extends RelationManager
{
    protected static string $relationship = 'products';

    /**
     * Define the form fields for creating new products
     * Currently only includes the product name field
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                // Product name field with translation support
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    /**
     * Define the table columns and actions for managing products
     * Includes product listing, attachment with catalog sync, and detachment capabilities
     */
    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product_id')
            ->columns([
                // Product name column
                Tables\Columns\TextColumn::make('name'),
            ])
            ->filters([
                // No filters defined
            ])
            ->headerActions([
                // Action to attach existing products to the discount
                AttachAction::make()
                    ->label(__('filament-panels.categories.action.addProduct'))
                    ->recordSelect(function ($livewire) {
                        // Get the current discount
                        $category = $livewire->getOwnerRecord();

                        // Get IDs of already attached products
                        $attachedProductIds = $category->products()->pluck('products.id')->toArray();

                        // Create a select field for choosing products
                        return Select::make('recordId')
                            ->label(__('filament-panels.categories.action.selectProduct'))
                            ->options(
                                // Only show products that aren't already attached
                                Products::whereNotIn('id', $attachedProductIds)
                                    ->pluck('name', 'id')
                            )
                            ->searchable()
                            ->preload();
                    })
                    ->after(function ($livewire, $data) {
                        // Get the discount (the parent record)
                        $discount = $livewire->getOwnerRecord();
            
                        // Get the attached product ID from the action's form data
                        $productId = $data['recordId'];
                
                        // Get the product
                        $product = Products::find($productId);
                
                        // Check if the discount should start now
                        if ($discount && ($discount->start_at === null || $discount->start_at <= now())) {
                            // Dispatch sync job to update catalog with discounted product
                            SyncDiscountProductToCatalogJob::dispatch($product, $discount, 'sync');
                        }
                    }),
            ])
            ->actions([
                // Action to detach a single product
                Tables\Actions\DetachAction::make()
            ])
            ->bulkActions([
                // Bulk actions for detaching multiple products
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                ]),
            ]);
    }
}
