<?php

namespace App\Filament\Tutor\Widgets;

use App\Enums\TutorActivities;
use App\Models\Question;
use App\Models\Tutor;
use App\Models\TutorActivity;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TutorPerformanceStats extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $user = Auth::user();
        $tutor = Tutor::where('user_id', $user->id)->first();

        // Total questions answered
        $answered = Question::where('tutor_id', $tutor->id)
            ->whereNotNull('answer_text')
            ->count();

        // Pending questions
        $pending = Question::where('tutor_id',  $tutor->id)
            ->whereNull('answer_text')
            ->count();

        $answeredActivities = TutorActivity::where('tutor_id', $tutor->id)
            ->where('action', TutorActivities::Answer)
            ->with('question')
            ->get();

        $totalSeconds = 0;
        $count = 0;

        foreach ($answeredActivities as $activity) {
            if ($activity->question && $activity->question->created_at) {
                $diff = $activity->created_at->diffInSeconds($activity->question->created_at, false);
                if ($diff < 0) {
                    $diff = abs($diff);
                }
                Log::info('activity->created_at', [$activity->created_at]);
                Log::info('activity->question->created_at', [$activity->question->created_at]);
                Log::info('diss', [$diff]);
                // dd($diff);
                $totalSeconds += $diff;
                $count++;
            }
        }

        $avgResponseMinutes = $count > 0 ? round(($totalSeconds / $count) / 60, 1) : 0;

        return [
            Stat::make(__('filament-panels.dashboard.completed_count'), $answered)
                ->description(__('filament-panels.dashboard.completed_desc'))
                ->color('success'),

            Stat::make(__('filament-panels.dashboard.pending_count'), $pending)
                ->description(__('filament-panels.dashboard.pending_desc'))
                ->color('warning'),

            Stat::make(__('filament-panels.dashboard.answered_avg'), $avgResponseMinutes . ' د')
                ->description(__('filament-panels.dashboard.answered_avg_desc'))
                ->color('info'),
        ];
    }
}
