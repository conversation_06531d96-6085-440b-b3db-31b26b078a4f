<?php
/**
 * Orders Chart Widget
 * 
 * Displays order statistics in a line chart with multiple time period views:
 * - Weekly view: Shows daily orders for the current week
 * - Monthly view: Shows daily orders for the current month
 * - Yearly view: Shows monthly orders for the current year
 * 
 * Supports localization for dates and labels
 */

namespace App\Filament\Widgets;

use App\Models\Orders;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrdersChart extends ChartWidget
{
    // Default time period filter
    public ?string $filter = 'week';
    
    // Widget display order in dashboard
    protected static ?int $sort = 2;
    
    // Widget spans full width in the dashboard
    protected int | string | array $columnSpan = 'full';
    
    // Maximum height constraint for the chart
    protected static ?string $maxHeight = '300px';

    // Get translated widget heading
    public function getHeading(): string
    {
        return __('filament-panels.chart.orders_chart');
    }

    /**
     * Prepare and return chart data based on selected time period
     * Handles weekly, monthly, and yearly views with proper date formatting
     */
    protected function getData(): array
    {
        $data = collect();
        $activeFilter = $this->filter;
        Log::info('filter: ' . $activeFilter);
        
        // Set locale for translated date formats
        $locale = app()->getLocale();
        Carbon::setLocale($locale);

        if ($activeFilter == 'week') {
            // Weekly View: Show orders for each day of current week
            $startOfWeek = now()->startOfWeek();
            $endOfWeek = now()->endOfWeek();

            // Generate all 7 days for this week
            $weekDays = collect();
            for ($i = 0; $i <= 6; $i++) {
                $date = $startOfWeek->copy()->addDays($i);
                $weekDays->push([
                    'date' => $date->format('Y-m-d'),
                    'label' => $date->translatedFormat('l'),  // Translated day name
                ]);
            }

            // Get orders grouped by date
            $orderCounts = Orders::select(
                DB::raw("DATE(created_at) as date"),
                DB::raw("COUNT(*) as count")
            )
                ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
                ->groupBy('date')
                ->pluck('count', 'date');

            // Fill in missing days with count = 0
            $data = $weekDays->map(function ($day) use ($orderCounts) {
                return [
                    'label' => $day['label'],
                    'count' => $orderCounts[$day['date']] ?? 0,
                ];
            });
        } else if ($activeFilter == 'month') {
            // Monthly View: Show orders for each day of current month
            $start = Carbon::now()->startOfMonth();
            $end = Carbon::now()->startOfDay(); // Only up to today

            // Create list of days from 1st of month to today
            $daysInMonth = collect();
            for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
                $daysInMonth->push([
                    'date' => $date->format('Y-m-d'),
                    'label' => $date->translatedFormat('d M'),  // Format: "01 Jan"
                ]);
            }

            // Get order counts grouped by date
            $orders = Orders::selectRaw('DATE(created_at) as date')
                ->whereBetween('created_at', [$start, $end])
                ->get()
                ->groupBy('date')
                ->map(fn($group) => $group->count());

            // Map counts to day list, fill missing with 0
            $data = $daysInMonth->map(function ($day) use ($orders) {
                return [
                    'label' => $day['label'],
                    'count' => $orders[$day['date']] ?? 0,
                ];
            });
        } else if ($activeFilter == 'year') {
            // Yearly View: Show orders for each month of current year
            $start = Carbon::now()->startOfYear();
            $end = Carbon::now()->startOfMonth(); // Only until current month

            // Build list of months from January to current month
            $months = collect();
            for ($date = $start->copy(); $date->lte($end); $date->addMonth()) {
                $months->push([
                    'month' => $date->format('Y-m'),
                    'label' => $date->translatedFormat('F'),  // Translated month name
                ]);
            }

            // Get order counts grouped by month
            $orders = Orders::select(
                DB::raw("DATE_FORMAT(created_at, '%Y-%m') as month"),
                DB::raw('COUNT(*) as count')
            )
                ->whereYear('created_at', now()->year)
                ->groupBy("month")
                ->pluck('count', 'month');

            // Map counts to month list, fill missing with 0
            $data = $months->map(function ($item) use ($orders) {
                return [
                    'label' => $item['label'],
                    'count' => $orders[$item['month']] ?? 0,
                ];
            });
        }

        $labels = $data->pluck('label')->toArray();
        $counts = $data->pluck('count')->toArray();

        // Return formatted data for chart
        return [
            'datasets' => [
                [
                    'label' => __('filament-panels.orders.title'),
                    'tension' => 0.3,              // Line curve smoothness
                    'data' => $counts,
                    'backgroundColor' => 'rgba(241, 217, 94, 0.53)',  // Area fill color
                    'borderColor' => 'rgb(245, 208, 23)',             // Line color
                ],
            ],
            'labels' => $labels,
        ];
    }

    /**
     * Configure chart display options
     * Sets up axes, scales, and grid display
     */
    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'title' => [
                        'display' => true,
                        'text' => __('filament-panels.orders.title'),      // Y-axis label
                    ],
                    'beginAtZero' => true,       // Start Y-axis from 0
                ],
                'x' => [
                    'grid' => [
                        'display' => false,      // Hide X-axis grid lines
                        'text' => 'Date',
                    ],
                ],
            ],
        ];
    }

    /**
     * Define available time period filters
     * Returns translated filter options
     */
    protected function getFilters(): ?array
    {
        return [
            'week' => __('filament-panels.chart.last_week'),
            'month' => __('filament-panels.chart.monthly_analysis'),
            'year' => __('filament-panels.chart.year'),
        ];
    }

    /**
     * Set chart type to line chart
     */
    protected function getType(): string
    {
        return 'line';
    }
}
