<?php

namespace App\Jobs;

use App\Models\JobProcesses;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Bus\Batch;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\Middleware\RateLimited;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class CheckUsersAndCreate implements ShouldQueue
{
    use Queueable;

    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function middleware()
    {
        return [(new WithoutOverlapping())];
    }

    public function handle(): void
    {
        $Job = JobProcesses::latest()->first();
        Log::info('Job tabel' . $Job);

        if (!$Job || $Job->total_chunks === $Job->completed_chunks) {
            Log::info('Job is start');

            // Fetch Subscription Plan where is active 
            $plans = SubscriptionPlan::where('is_active', 1)->get();
            $totalChunksToAdd = 0;
            $chunkJobs = [];
            foreach ($plans as $plan) {
                //Count the number of the subscription accounts available for the plan
                $accountCount = Subscription::where("subscription_plan_id", $plan->id)->where('status', 0)->count();

                // Check if the user accountCount is less than 10k
                if ($accountCount < 10000) {
                    Log::info("{$plan->title} User count is {$accountCount}, less then 10k.");

                    Log::info('plan: ' . $plan->title);
                    try {
                        $days = $plan->period;
                        $plan_id = $plan->id;
                        Log::info("One chunk job has been added to the list.");
                        $chunkJobs[] = new  ProcessAccountData(['number' => 100, 'number_days' => $days, 'plan' => $plan_id, 'job_process' => 1]);
                        $totalChunksToAdd++;
                        
                    } catch (\Exception $e) {
                        Log::error('Something went wrong ' . $e->getMessage());
                    }
                } else {
                    Log::info("{$plan->title} User count is already {$accountCount} ,more then 10k.");
                }
            }
            try {
                if (!$Job) {
                    // Create job processes if not exists 
                    JobProcesses::create(["total_chunks" => $totalChunksToAdd, "completed_chunks" => 0,]);
                } else {

                    $Job->update(["total_chunks" => $totalChunksToAdd, "completed_chunks" => 0]);
                }
                //Dispatch the job for each batch
                Bus::batch($chunkJobs)
                    ->catch(function (Batch $batch, Throwable $e) {
                        Log::error('One or more chunk jobs failed: ' . $e->getMessage());
                    })
                    ->finally(function (Batch $batch) {
                        Log::info('Batch processing finished.');
                    })
                    ->dispatch();
            } catch (\Exception $e) {
                Log::error('Something went wrong ' . $e->getMessage());
            }
        }
    }
}
