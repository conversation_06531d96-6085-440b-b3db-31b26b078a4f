<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TutorQuestionSessions extends Model
{
    protected $fillable = [
        'whatsapp_number',
        'question_id',
        'answer_text',
        'answer_url',
        'answer_id',
        'answer_type'
    ];
    protected $casts = [
        'id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    public function question(): BelongsTo 
    {
        return $this->belongsTo(Question::class);
    }
}
