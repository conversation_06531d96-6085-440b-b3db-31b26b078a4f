<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TutorActivities;
use App\Models\Question;
use App\Models\Tutor;
use App\Models\TutorActivity;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class AdminGeneralStats extends BaseWidget
{
    protected static ?int $sort =6;

    protected function getStats(): array
    {
        $now = now();
        $weekAgo = now()->subDays(7);

        $questions = Question::query();
        $activities = TutorActivity::query();

        return [
            Stat::make(__('filament-panels.dashboard.total_questions'), $questions->count())
                ->description(__('filament-panels.dashboard.total_questions_desc'))
                ->icon('heroicon-o-question-mark-circle')
                ->color('primary')
                ->chart($this->getTrend(
                    fn($date) =>
                    Question::whereDate('created_at', $date)->count()
                )),

            Stat::make(__('filament-panels.dashboard.answered_questions'), $questions->whereNotNull('answer_text')->count())
                ->description(__('filament-panels.dashboard.answered_questions_desc'))
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->chart($this->getTrend(
                    fn($date) =>
                    Question::whereDate('created_at', $date)->whereNotNull('answer_text')->count()
                )),

            Stat::make(__('filament-panels.dashboard.unanswered_questions'), $questions->whereNull('answer_text')->count())
                ->description(__('filament-panels.dashboard.unanswered_questions_desc'))
                ->icon('heroicon-o-exclamation-circle')
                ->color('warning')
                ->chart($this->getTrend(
                    fn($date) =>
                    Question::whereDate('created_at', $date)->whereNull('answer_text')->count()
                )),

            Stat::make(__('filament-panels.dashboard.questions_this_week'), Question::where('created_at', '>=', $weekAgo)->count())
                ->description(__('filament-panels.dashboard.questions_this_week_desc'))
                ->icon('heroicon-o-calendar')
                ->color('info'),

            Stat::make(__('filament-panels.dashboard.avg_answer_time'), $this->getAverageAnswerTime())
                ->description(__('filament-panels.dashboard.avg_answer_time_desc'))
                ->icon('heroicon-o-clock')
                ->color('gray'),

            Stat::make(__('filament-panels.dashboard.total_tutors'), Tutor::count())
                ->description(__('filament-panels.dashboard.total_tutors_desc'))
                ->icon('heroicon-o-user-group')
                ->color('primary'),

            Stat::make(__('filament-panels.dashboard.accepted_activities'), $activities->where('action', TutorActivities::Accept)->count())
                ->description(__('filament-panels.dashboard.accepted_activities_desc'))
                ->icon('heroicon-o-check')
                ->color('info')
                ->chart($this->getTrend(
                    fn($date) =>
                    TutorActivity::where('action', TutorActivities::Accept)->whereDate('created_at', $date)->count()
                )),

            Stat::make(__('filament-panels.dashboard.answered_activities'), $activities->where('action', TutorActivities::Answer)->count())
                ->description(__('filament-panels.dashboard.answered_activities_desc'))
                ->icon('heroicon-o-pencil-square')
                ->color('success')
                ->chart($this->getTrend(
                    fn($date) =>
                    TutorActivity::where('action', TutorActivities::Answer)->whereDate('created_at', $date)->count()
                )),

            Stat::make(__('filament-panels.dashboard.edited_activities'), $activities->where('action', TutorActivities::Edit)->count())
                ->description(__('filament-panels.dashboard.edited_activities_desc'))
                ->icon('heroicon-o-pencil')
                ->color('warning')
                ->chart($this->getTrend(
                    fn($date) =>
                    TutorActivity::where('action', TutorActivities::Edit)->whereDate('created_at', $date)->count()
                )),
        ];
    }

    protected function getAverageAnswerTime(): string
    {
        $avgSeconds = TutorActivity::query()
            ->where('tutor_activities.action', TutorActivities::Answer)
            ->whereNotNull('tutor_activities.created_at')
            ->join('questions', 'tutor_activities.question_id', '=', 'questions.id')
            ->avg(DB::raw('TIMESTAMPDIFF(SECOND, questions.created_at, tutor_activities.created_at)'));

        return $avgSeconds ? round($avgSeconds / 60, 1) . ' min' : '—';
    }
    protected function getTrend(callable $queryCallback): array
    {
        $trend = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->startOfDay();
            $trend[] = $queryCallback($date);
        }

        return $trend;
    }
}
