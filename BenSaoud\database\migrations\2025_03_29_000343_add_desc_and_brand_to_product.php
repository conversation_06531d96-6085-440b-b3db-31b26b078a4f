<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('brand')->nullable()->after('name');
            $table->string('desc')->after('name');
            $table->string('website_link')->after('name');
            $table->bigInteger('product_catelog_id')->nullable()->after('name');
        });
    }
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['categories_id']);
        });

        Schema::dropIfExists('products');
    }
};
