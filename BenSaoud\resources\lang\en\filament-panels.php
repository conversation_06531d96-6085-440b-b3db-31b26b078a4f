
<?php

return [
    'title' => 'Dashboard',
    'actions' => [
        'create' => 'Create',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'view' => 'View',

    ],
    'dashboard' => [
        'title' => 'Ben Saoud',
        'data' => [
            'allOrders' => 'Total Orders',
            'pending' => 'Number of pending product purchase requests',
            'rejected' => 'Number of rejected product purchase requests',

            'allCustomerTitle' => 'Total Customers',
            'buyerCustomerTitle' => 'Buyers',
            'chatCustomerTitle' => 'Chat Only',

            'allCustomer' => 'All customers who contacted through WhatsApp',
            'buyerCustomer' => 'Customers who placed at least one orde',
            'chatCustomer' => 'Chatted but didn’t buy',

        ]
    ],
    'products' => [
        'title' => 'Products',
        'singular' => 'Product',
        'plural' => 'Products',
        'fields' => [
            'name' => 'Product name',
            'price' => 'price',
            'code' => 'Product code',
            'quantity' => 'quantity',
            'categories' => 'categories',
            'image' => 'Image',
            'revenues' => 'Revenues',
            'link' => 'Website Link',
            'desc' => "Praduct description",
            'created_at' => 'Created at ',
            'updated_at' => 'Updated at',
            'discount'  => 'Discount',
            'discountPrice'=>'Price After Discount'


        ],
    ],
    'categories' => [
        'title' => 'Categories',
        'singular' => 'Category',
        'plural' => 'Categories',
        'fields' => [
            'name' => 'Category name',
            'created_at' => 'Created at ',
            'updated_at' => 'Updated at',
        ],
        'action' => [
            'addProduct' => 'Add Product',
            'selectProduct' => 'Select Product'
        ]
    ],
    'orders' => [
        'title' => 'Orders',
        'singular' => 'Order',
        'plural' => 'Orders',
        'fields' => [
            'name' => 'Name',
            'phone' => 'Phone Number',
            'payment' => 'Payment method',
            'price' => 'Total price',
            'created_at' => 'Created at ',
            'updated_at' => 'Updated at',
            'orderInfo' => 'Order Info',

        ],
        "print" => 'Print'

    ],
    'ordersItems' => [
        'title' => 'Order Items',
        'singular' => 'Order Items',
        'plural' => 'Order Items',
        'fields' => [
            'orderId' => 'Order Id',
            'productName' => 'Product name',
            'quantity' => 'Quantity',
            'price' => 'Price',
            'status' => 'Status',
            'created_at' => 'Created at ',
            'updated_at' => 'Updated at',

        ],
        "action" => [
            'all' => 'All Orders',
            'pending' => 'Pending',
            'rejected' => 'Rejected'
        ]
    ],
    'transactions' => [
        'title' => 'Transactions',
        'singular' => 'Transaction',
        'plural' => 'Transactions',
        'fields' => [
            'name' => 'name',
            'amount' => 'Amount',
            'status' => 'Status',
            'payment_provider' => 'Payment Provider',
            'created_at' => 'Created at ',
            'updated_at' => 'Updated at',
        ],
        'totalAmount' => 'Total amount',
        'TotalTransactions' => 'Total Transactions',
        'pending' => 'Pending',
        'paid' => 'Paid',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled'
    ],

    'discount' => [
        'title' => 'Discounts',
        'singular' => 'Discount',
        'plural' => 'Discounts',
        'fields' => [
            'name' => 'Name',
            'value' => 'Discount',
            'start' => 'Start at',
            'end' => 'End at',
        ],
    ],
    'storeBranch' => [
        'title' => 'Store Branchs',
        'singular' => 'Store Branch',
        'plural' => 'Store Branchs',
        'fields' => [
            'name' => 'Name',
            'address' => 'Address',
            'phone' => 'Phone'
        ]
    ],
    'setting' => [
        'title' => 'Settings',
        'singular' => 'Setting',
        'plural' => 'Settings',
        'templateInfo' => "Download product template file for Meta WhatsApp Catalog.",
        'general'=>'General',
        'catalog'=>'Catalog Template',
        'nameAr'=>'Name (Arabic)',
        'nameEn' =>'Name (English)',
        'phone' =>'Store phone number in WhatsApp',
        'logo' => 'Logo',
        'catalogId' => 'Catalog ID in Facebook (meta)',
        'password'=>"Password",
        'welcomeMessage'=>"Welcome message",


    ],
    'chart' => [
        'current_month_revenue' => 'Current Month Revenue',
        'previous_month_revenue' => 'Previous Month Revenue',
        'current_month_orders' => 'Current Month Orders',
        'monthly_sales' => 'Monthly Sales',
        'current_quarter_revenue' => 'Current Quarter Revenue',
        'previous_quarter_revenue' => 'Previous Quarter Revenue',
        'quarterly_comparison' => 'Quarterly Comparison',
        'revenue_lyd' => 'Revenue (LYD)',
        'number_of_orders' => 'Number of Orders',
        'monthly_analysis' => 'Monthly Analysis',
        'quarterly_analysis' => 'Quarterly Analysis',
        'yearly_analysis' => 'Yearly Analysis',
        'yearly_revenue' => 'Yearly Revenue',
        'revenue_for_year' => 'Revenue :year',
        'sales_performance_analysis'=>'Sales Performance Analysis',
        'orders_chart'=>'Orders Chart',
        'last_week'=>'Last Week',
        'year'=>'Year',
        'payment_methods'=>'Payment Methods',
        'transaction_status'=>'Transaction Status',
        'top_customers'=>'Top Customers',
        'top_selling_products'=>'Top Selling Products',
        'whatsapp_customer_stats'=>'WhatsApp Customer Stats',
        'analysis_report' => [
            'title' => 'Analysis Report',
            'download' => 'Download Analysis Report',
            'generated_at' => 'Generated at',
            'orders_summary' => 'Orders Summary',
            'total_orders' => 'Total Orders',
            'total_revenue' => 'Total Revenue',
            'this_month_orders' => 'This Month Orders',
            'this_month_revenue' => 'This Month Revenue',
            'last_month_orders' => 'Last Month Orders',
            'last_month_revenue' => 'Last Month Revenue',
            'payment_methods_distribution' => 'Payment Methods Distribution',
            'payment_method' => 'Payment Method',
            'number_of_orders' => 'Number of Orders',
            'percentage' => 'Percentage',
            'recent_orders' => 'Recent Orders',
            'order_id' => 'Order ID',
            'customer' => 'Customer',
            'amount' => 'Amount',
            'date' => 'Date',
            'page' => 'Page',
            'footer' => 'Ben Saoud - Analysis Report',
        ],
    ],
    'payment_methods' => [
        'cash' => 'Cash',
        'bank' => 'Bank',
        'card' => 'Card',
        'unspecified' => 'Unspecified',
        'title' => 'Payment Methods',
        'total' => 'Total Orders',
    ],
    'report'=>[
        'title'=>'Reports',
        'inventory_report'=>'Inventory Report',
        'report_download' => 'Download Report',
        'inventory_report_description'=>'Show a comprehensive analysis of sales and revenues, including total sales, average order value, and product performance.',
        'sales_revenue_report'=>'Sales & Revenue Performance Report',
        'sales_revenue_report_description'=>'Show a comprehensive analysis of sales and revenues, including total sales, average order value, and product performance.',
    ]
];
