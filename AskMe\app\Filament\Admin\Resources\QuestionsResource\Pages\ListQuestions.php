<?php

namespace App\Filament\Admin\Resources\QuestionsResource\Pages;

use App\Filament\Admin\Resources\QuestionsResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;

class ListQuestions extends ListRecords
{
    protected static string $resource = QuestionsResource::class;

    public function getTabs(): array
    {
        return [
            'unanswered' => Tab::make(__("filament-panels.question.tabs.unanswered"))
                ->modifyQueryUsing(
                    fn($query) => $query->whereNull('answer_text')
                        ->whereNull('answer_url')
                ),
            'answered' => Tab::make(__("filament-panels.question.tabs.answered"))
                ->modifyQueryUsing(
                    fn($query) => $query->where(function ($q) {
                        $q->whereNotNull('answer_text')
                            ->orWhereNotNull('answer_url');
                    })
                ),
            'all' => Tab::make(__("filament-panels.question.tabs.all")),

        ];
    }
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
