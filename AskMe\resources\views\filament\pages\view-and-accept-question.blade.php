<x-filament::page>
<div class="w-full space-y-6 px-4 sm:px-6 lg:px-8">
        <div class="bg-white border p-6 rounded-xl shadow-sm">
        <div class="mt-4 text-sm text-gray-600 space-y-1">
                <div><strong>{{__('filament-panels.question.fields.name')}}:</strong> {{ $question->student->name ?? '—' }}</div>
                <div><strong>{{__('filament-panels.question.fields.grade')}}:</strong> {{ $question->grade->name ?? '—' }}</div>
                <div><strong>{{__('filament-panels.question.fields.subject')}}:</strong> {{ $question->subject->name ?? '—' }}</div>
                <div></strong> {{ $question->created_at->format('Y-m-d H:i') }}</div>
            </div>
            <h2 class="text-lg font-bold text-primary mb-2">{{__('filament-panels.question.singular')}} :</h2>
            <p class="text-gray-800">{{ $question->question_text }}</p>

            @if ($question->image_url)
                <img src="{{ asset($question->image_url) }}" class="mt-4 w-full max-w-xs rounded shadow">
            @endif

        
        </div>

        @if ($question->tutor_id === null)
            <x-filament::button wire:click="accept" color="success">
            {{__('filament-panels.question.action.accept')}}
            </x-filament::button>
        @else
            <x-filament::badge color="gray">Already accepted</x-filament::badge>
        @endif
    </div>
</x-filament::page>
