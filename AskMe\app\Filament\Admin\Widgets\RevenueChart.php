<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TransactionStatus;
use App\Models\Transaction;
use Filament\Actions\Action;
use Filament\Widgets\ChartWidget;

class RevenueChart extends ChartWidget
{
    protected static ?string $heading = 'إحصائيات الإيرادات';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    public ?string $filter = 'month'; // default

    
    protected function getFilters(): ?array
    {
        return [
            'week' => __('filament-panels.dashboard.range_week'),
            'month' => __('filament-panels.dashboard.range_month'),
            'year' => __('filament-panels.dashboard.range_year'),
        ];
    }
    protected function getData(): array
    {
        $filter = $this->filter ?? 'month';
        $labels = [];
        $data = [];

        if ($filter === 'week') {
            $start = now()->startOfWeek();
            $end = now()->endOfWeek();

            for ($i = 0; $i <= 6; $i++) {
                $day = $start->copy()->addDays($i);
                $labels[] = __($day->translatedFormat('l'));
                $dailyTotal = Transaction::where('status',  TransactionStatus::COMPLETED)
                    ->whereDate('created_at', $day)
                    ->sum('amount');
                $data[] = round($dailyTotal, 2);
            }
        } elseif ($filter === 'month') {
            $start = now()->startOfMonth();
            $end = now()->startOfDay();

            for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
                $labels[] = $date->translatedFormat('d M');
                $dailyTotal = Transaction::where('status', TransactionStatus::COMPLETED)
                    ->whereDate('created_at', $date)
                    ->sum('amount');
                $data[] = round($dailyTotal, 2);
            }
        } elseif ($filter === 'year') {
            for ($i = 11; $i >= 0; $i--) {
                $month = now()->subMonths($i)->startOfMonth();
                $labels[] = $month->translatedFormat('F');
                $monthlyTotal = Transaction::where('status',  TransactionStatus::COMPLETED)
                    ->whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->sum('amount');
                $data[] = round($monthlyTotal, 2);
            }
        }

        return [
            'datasets' => [
                [
                    'label' => __('filament-panels.dashboard.revenue'),
                    'data' => $data,
                    'borderColor' => '#3b82f6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
