<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>فاتورة #{{ $order->id }}</title>
    <style>
        body {
            font-family: Tahoma, sans-serif;
            direction: rtl;
            padding: 40px;
            color: #333;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
        }

        .invoice-info {
            margin-bottom: 20px;
        }

        .invoice-info p {
            margin: 4px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 25px;
        }

        th, td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: center;
        }

        th {
            background-color: #f5f5f5;
        }

        .total {
            margin-top: 20px;
            font-size: 1.2em;
            font-weight: bold;
            text-align: left;
        }

        .no-print {
            margin-top: 30px;
            text-align: center;
        }

        @media print {
            .no-print { display: none; }
        }
    </style>
</head>
<body>

    <header>
        <h2> فاتورة الطلب رقم #{{ $order->id }}</h2>
        {{-- <img src="{{ asset('images/logo.png') }}" height="60" alt="شعار المتجر"> --}}
    </header>

    <div class="invoice-info">
        <p><strong>العميل:</strong> {{ $order->customer->name }}</p>
        <p><strong>رقم الواتساب:</strong> {{ $order->customer->whatsapp_number }}</p>
        <p><strong>تاريخ الطلب:</strong> {{ $order->created_at->format('Y-m-d H:i') }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>المنتج</th>
                <th>الكمية</th>
                <th>السعر</th>
                <th>الاجمالي</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($order->orderItems  as $item)
                <tr>
                    <td>{{ $item->product->name }}</td>
                    <td>{{ $item->quantity }}</td>
                    <td>{{ number_format($item->price, 2) }} LYD</td>
                    <td>{{ number_format($item->quantity * $item->price, 2) }} LYD</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="total">
        الإجمالي الكلي: {{ number_format($order->total_price, 2) }} LYD
    </div>

    <div class="no-print">
        <button onclick="window.print()">🖨️ طباعة</button>
    </div>

</body>
</html>
