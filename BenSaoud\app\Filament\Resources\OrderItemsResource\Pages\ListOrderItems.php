<?php

namespace App\Filament\Resources\OrderItemsResource\Pages;

use App\Enums\OrderStatus;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\OrderItemsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

/**
 * ListOrderItems
 * 
 * Page component for listing order items in the Filament admin panel.
 * Provides tabbed views to filter orders by their status (All, Pending, Rejected).
 * Uses translation keys for internationalization of labels.
 */
class ListOrderItems extends ListRecords
{
    protected static string $resource = OrderItemsResource::class;

    /**
     * Define the tabs for filtering order items by status
     * 
     * @return array Array of Tab components with their respective query filters
     */
    public function getTabs(): array
    {
        return [
            // Tab showing all order items
            __('filament-panels.ordersItems.action.all') => Tab::make(),
            
            // Tab showing only pending order items
            __('filament-panels.ordersItems.action.pending') => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', OrderStatus::PENDING)),
            
            // Tab showing only rejected order items
            __('filament-panels.ordersItems.action.rejected') => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', OrderStatus::REJECTED)),
        ];
    }
    
    /**
     * Define the header actions available on this page     * 
     * @return array Array of header actions
     */
    protected function getHeaderActions(): array
    {
        return [
          
        ];
    }
}
