<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserProductSessionsTable extends Migration
{
    public function up()
    {
        Schema::create('user_product_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained();
            $table->string('session_type');
            $table->string('reference_id');
            $table->integer('last_product_id');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_product_sessions');
    }
}