<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('subscription', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained();
            $table->foreignId('subscription_packages_id')->constrained();
            $table->integer('total_questions');
            $table->integer('used_questions');
            $table->timestamps();    
        });
    }

    public function down(): void {
        Schema::dropIfExists('subscription');
    }
};
