<?php

namespace App\Filament\Admin\Resources;

use App\Enums\SubscriptionPlanType;
use App\Filament\Admin\Resources\SubscriptionPackageResource\Pages;
use App\Filament\Admin\Resources\SubscriptionPackageResource\RelationManagers;
use App\Models\SubscriptionPackage;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubscriptionPackageResource extends Resource
{
    protected static ?string $model = SubscriptionPackage::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public function getTitle(): string
    {
        return __('filament-panels.subscription_pachage.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.subscription_pachage.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.subscription_pachage.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.subscription_pachage.title');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')
                ->required()
                ->minLength(3)
                ->maxLength(100)
                ->label(__('filament-panels.subscription_pachage.fields.name')),

            TextInput::make('question_limit')
                ->numeric()
                ->minValue(1)
                ->required()
                ->label(__('filament-panels.subscription_pachage.fields.question_limit')),

            TextInput::make('price')
                ->numeric()
                ->minValue(0)
                ->label(__('filament-panels.subscription_pachage.fields.price'))
                ->required(),

            Select::make('type')
                ->required()
                ->options(collect(SubscriptionPlanType::cases())->mapWithKeys(fn($case) => [
                    $case->value => $case->label()
                ])->toArray())
                ->label(__('filament-panels.subscription_pachage.fields.type')),

            Toggle::make('is_active')
                ->label(__('filament-panels.subscription_pachage.fields.status'))
                ->default(1),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->searchable()->sortable()->label(__('filament-panels.subscription_pachage.fields.name')),

                TextColumn::make('question_limit')->label('Limit')->sortable()->label(__('filament-panels.subscription_pachage.fields.question_limit')),

                TextColumn::make('price')
                    ->formatStateUsing(function ($state, $record) {
                        $currency = match ($record->type) {
                            SubscriptionPlanType::LOCAL->value => 'د.ل', 
                            SubscriptionPlanType::INTERNATIONAL->value  => '$', 
                            default => '',
                        };

                        return $currency . ' ' . number_format($state, 2);
                    })
                    ->sortable()->label(__('filament-panels.subscription_pachage.fields.price')),

                TextColumn::make('type')
                    ->label(__('filament-panels.subscription_pachage.fields.type'))->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'LOCAL' => 'primary',
                        'INTERNATIONAL' => 'success',
                        default => 'gray',
                    })
                    ->sortable(),

                ToggleColumn::make('is_active')->label(__('filament-panels.subscription_pachage.fields.status'))

            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptionPackages::route('/'),
            'create' => Pages\CreateSubscriptionPackage::route('/create'),
            'edit' => Pages\EditSubscriptionPackage::route('/{record}/edit'),
        ];
    }
}
