<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrdersResource\Pages;
use App\Models\Orders;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\Filter;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;


class OrdersResource extends Resource
{
    // Defines the Eloquent model that this Filament resource manages.
    protected static ?string $model = Orders::class;

    // Sets the icon that will be displayed next to the resource in the Filament sidebar navigation.
    protected static ?string $navigationIcon = 'heroicon-s-shopping-cart';

    /**
     * Defines the form schema for creating and editing Order records.
     *
     * @param Form $form
     * @return Form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    /**
     * Defines the table schema for listing Order records.
     *
     * @param Table $table
     * @return Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Text column to display the 'id' of the order.
                TextColumn::make('id')
                    ->sortable() 
                    ->label('#'), 

                // Text column to display the customer's name from the 'customer' relationship.
                TextColumn::make('customer.name')
                    ->searchable() // Enables searching by customer name
                    ->label(__('filament-panels.orders.fields.name')), 

                // Text column to display the total price of the order, formatted as currency.
                TextColumn::make('total_price')
                    ->label(__('filament-panels.orders.fields.price')) 
                    ->formatStateUsing(fn($state) => number_format($state, 2) . ' د.ل'), // Formats price with 2 decimal places and "LYD" (Libyan Dinar) suffix

                // Text column to display the creation timestamp of the order.
                TextColumn::make('created_at')
                    ->dateTime() // Formats as date and time
                    ->label(__('filament-panels.orders.fields.created_at')),

                // Text column to display the last update timestamp of the order.
                TextColumn::make('updated_at')
                    ->dateTime() // Formats as date and time
                    ->label(__('filament-panels.orders.fields.updated_at')), 
            ])
            ->filters([
                // Filter for 'created_at' date range.
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('created_from'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'], // Apply filter only if 'created_from' is provided
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date), 
                            );
                    })
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * Defines the infolist schema for displaying details of a single Order record.
     *
     * @param Infolist $infolist
     * @return Infolist
     */
    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Section to group general order information.
                Section::make(__('filament-panels.orders.fields.orderInfo'))->schema([
                    // Text entry to display the order ID.
                    TextEntry::make('id')
                        ->label(__('filament-panels.ordersItems.fields.orderId')), 

                    // Text entry to display the customer's name.
                    TextEntry::make('customer.name')
                        ->label(__('filament-panels.orders.fields.name')), 

                    // Text entry to display the customer's WhatsApp number.
                    TextEntry::make('customer.whatsapp_number')
                        ->label(__('filament-panels.orders.fields.phone')), 

                    // Text entry to display the total price, formatted as currency.
                    TextEntry::make('total_price')
                        ->label(__('filament-panels.orders.fields.price')) 
                        ->formatStateUsing(fn($state) => number_format($state, 2) . ' د.ل'), // Formats price with 2 decimal places and "LYD" suffix

                    // Text entry to display the creation timestamp.
                    TextEntry::make('created_at')
                        ->dateTime() // Formats as date and time
                        ->label(__('filament-panels.orders.fields.created_at')), 

                    // Text entry to display the last update timestamp.
                    TextEntry::make('updated_at')
                        ->dateTime() // Formats as date and time
                        ->label(__('filament-panels.orders.fields.updated_at')), 

                    \Filament\Infolists\Components\Actions::make([
                        \Filament\Infolists\Components\Actions\Action::make('print_invoice')
                            ->label(__("filament-panels.orders.print")) 
                            ->icon('heroicon-o-printer') // Printer icon
                            ->url(fn ($record) => route('filament.admin.resources.orders.invoice', ['record' => $record->id])) // URL to a custom invoice route
                            ->openUrlInNewTab() // Opens the URL in a new browser tab
                    ]),
                ])->columns(2),
            ]);
    }

    /**
     * Get the singular label for the model associated with this resource.
     *
     * @return string
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.orders.singular');
    }

    /**
     * Get the plural label for the model associated with this resource.
     *
     * @return string
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.orders.plural');
    }

    /**
     * Get the navigation label for this resource in the Filament sidebar.
     *
     * @return string
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.orders.title');
    }

    /**
     * Defines the relationships that can be managed directly from this resource.
     *
     * @return array
     */
    public static function getRelations(): array
    {
        return [
        ];
    }

    /**
     * Defines the pages associated with this resource.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'), // Page to list all orders
            'create' => Pages\CreateOrders::route('/create'), // Page to create a new order
            'edit' => Pages\EditOrders::route('/{record}/edit'), // Page to edit an existing order
        ];
    }
}
