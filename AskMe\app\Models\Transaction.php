<?php

namespace App\Models;

use App\Enums\Currency;
use App\Enums\PaymentProvider;
use App\Enums\TransactionStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transaction extends Model
{

    protected $fillable = ['amount', 'currency', 'status', 'paymentProvider', 'uuid', 'externalRef', 'subscription_packages_id', 'student_id'];
    protected $casts = [
        'id' => 'integer',
        'subscription_id' => 'integer',
        'amount' => 'decimal:2',
        'email_send' => 'boolean',
        'text_send' => 'boolean',
        'payment_provider' => PaymentProvider::class,
        'currency' => Currency::class,
        'status' => TransactionStatus::class,
    ];


    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }
    public function subscriptionPackage(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPackage::class, 'subscription_packages_id');
    }
}
