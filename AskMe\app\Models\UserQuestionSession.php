<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserQuestionSession extends Model
{
    protected $fillable = [
        'whatsapp_number',
        'grade_id',
        'subject_id',
    ];
    protected $casts = [
        'id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    public function subject(): BelongsTo 
    {
        return $this->belongsTo(Subject::class);
    }
    public function grade(): BelongsTo
    {
        return $this->belongsTo(Grade::class);
    }
}
