<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SubjectResource\Pages;
use App\Filament\Admin\Resources\SubjectResource\RelationManagers;
use App\Models\Subject;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubjectResource extends Resource
{
    protected static ?string $model = Subject::class;

    protected static ?string $navigationIcon = 'heroicon-o-document';

    public function getTitle(): string
    {
        return __('filament-panels.subject.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.subject.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.subject.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.subject.title');
    }
    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')
                ->label(__('filament-panels.question.fields.subject'))
                ->required()
                ->minLength(3)
                ->maxLength(255),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('filament-panels.question.fields.subject'))
                    ->sortable()
                    ->searchable(),
                TextColumn::make('created_at')
                    ->label(__('filament-panels.question.fields.created_at'))

                    ->dateTime(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubjects::route('/'),
            'create' => Pages\CreateSubject::route('/create'),
            'edit' => Pages\EditSubject::route('/{record}/edit'),
        ];
    }
}
