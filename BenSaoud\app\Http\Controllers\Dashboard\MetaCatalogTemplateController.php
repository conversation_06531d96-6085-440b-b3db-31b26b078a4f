<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * Controller responsible for generating a Meta WhatsApp Catalog product template.
 * The template includes required and optional fields to guide users in preparing product data
 * for syncing with Meta's product catalog API.
 */
class MetaCatalogTemplateController extends Controller
{
    /**
     * Generate and download the Meta Catalog product template.
     *
     * @param string $format Either 'xlsx' or 'csv'
     * @return BinaryFileResponse
     */
    public function download(string $format = 'csv')
    {
        // Define the structure of the file
        $headers = ['name', 'retailer_id', 'price','quantity', 'image_url', 'description', 'url', 'brand'];
        $hints = [
            'Product name (required)',
            'Unique ID (required)',
            'Price (required',
            'Quantity (required)',
            'Public image URL List (required)',
            'Product description (optional)',
            'Product page URL (optional)',
            'Brand name (optional)',
        ];
        $sample = [
            'Classic Watch',
            'SKU-001',
            '1500',
            '2',
            'https://yourdomain.com/images/watch.jpg , https://yourdomain.com/images/watch.jpg ',
            'Elegant black wristwatch for men',
            'https://yourdomain.com/products/sku-001',
            'Casio',
        ];

        // Create the spreadsheet and add rows
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->fromArray([$headers, $hints, $sample]);

        // Generate a temporary filename
        $filename = 'meta_catalog_template_' . Str::random(8) . '.' . $format;
        $filePath = storage_path("app/{$filename}");

        // Create writer based on selected format
        if ($format === 'csv') {
            $writer = new Csv($spreadsheet);
            $writer->setDelimiter(',');
            $writer->setEnclosure('"');
            $writer->setLineEnding("\r\n");
            $writer->setSheetIndex(0);
        } else {
            $writer = new Xlsx($spreadsheet);
        }

        // Save the file to disk
        $writer->save($filePath);

        // Return the file as a download, then delete after send
        return response()->download($filePath)->deleteFileAfterSend(true);
    }
}
