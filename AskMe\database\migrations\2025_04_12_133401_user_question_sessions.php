<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_question_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('whatsapp_number')->unique(); 
            $table->foreignId('grade_id')->nullable()->constrained();
            $table->foreignId('subject_id')->nullable()->constrained();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
