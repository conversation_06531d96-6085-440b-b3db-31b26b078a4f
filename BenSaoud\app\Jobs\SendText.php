<?php

namespace App\Jobs;

use App\Models\Transaction;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>lio\Rest\Client;

/**
 * SendText Job
 * 
 * This job is responsible for sending WhatsApp template messages 
 * using the WhatsApp Business API. It supports dynamic message templates
 * and handles different message types based on the provided template type.
 * Usage:
 * dispatch(new SendText($phone, 'sendAccountData', ['John Doe', '1234']));
 * dispatch(new SendText($phone, 'Support', []));
 * */
class SendText implements ShouldQueue
{
    use Queueable;
    protected  $phone;
    protected  $variables;
    protected $templateType;
    /**
     * Constructor to initialize job properties.
     *
     * @param  $phone  phone number
     * @param  $templateType The type of template message to be sent
     * @param array $variables Dynamic values to be inserted into the template [email,password]
     */

    public function __construct($phone, $templateType, $variables = [],)
    {
        $this->phone = $phone;
        $this->variables = $variables;
        $this->templateType = $templateType;
    }

    public function handle(): void
    {
        try {
            $to = $this->phone;

            // Determine template name based on the template type
            $templateName = match ($this->templateType) {
                'sendAccountData' => config('api.whatsapp_template_account'),
                'Support'         => config('api.whatsapp_template_support'),
                default           => config('api.whatsapp_template_otp'),
            };

            //API URL for sending the message
            $url = config('api.whatsapp_api_url') . config('api.whatsapp_phone_id') . "/messages";

            $payload = [
                "messaging_product" => "whatsapp",
                "to" => $to,
                "type" => "template",
                "template" => [
                    "name" => $templateName,
                    "language" => ["code" => "ar"],
                ]
            ];
            // If the template type is sendAccountData, add dynamic variables to the message body
            if ($this->templateType === 'sendAccountData' || $this->templateType === 'otp') {
                $payload["template"]["components"] = [
                    [
                        "type" => "body",
                        "parameters" => array_map(fn($var) => ["type" => "text", "text" => $var], $this->variables)
                    ]
                ];
            }

            //  Add a button if this is an OTP template
            if ($this->templateType === 'otp') {
                $payload["template"]["components"][] = [
                    "type" => "button",
                    "sub_type" => "url",
                    "index" => "0",
                    "parameters" => array_map(fn($var) => ["type" => "text", "text" => $var], $this->variables)

                ];
            }
            Log::info("WhatsApp payload: ", [$payload]);

            // Send the request to the WhatsApp API with authentication
            $response = Http::withToken(config('api.whatsapp_access_token'))
                ->post($url, $payload);

            Log::info("WhatsApp Template Message Sent: " . json_encode($response->json()));
            if ($this->templateType === 'sendAccountData') {
                Transaction::where('phone', $this->phone)
                    ->latest('id') // Get the last inserted record
                    ->first()
                    ?->update(['text_send' => 1]); // Update only if found
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp message sending failed', ['error' => $e->getMessage()]);
        }
    }
}
