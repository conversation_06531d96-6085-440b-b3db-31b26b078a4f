<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Subscription extends Model
{

    protected $table = 'subscription';
    protected $fillable = ['student_id', 'subscription_packages_id', 'total_questions', 'used_questions'];
    protected $casts = [
        'id' => 'integer',
        'total_questions' => 'integer',
        'used_questions' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }
    public function package(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPackage::class, 'subscription_packages_id');
    }
}
