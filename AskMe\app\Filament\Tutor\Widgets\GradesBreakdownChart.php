<?php

namespace App\Filament\Tutor\Widgets;

use App\Models\Question;
use App\Models\Tutor;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GradesBreakdownChart extends ChartWidget
{
    protected static ?string $heading = '';
    protected static ?int $sort = 2;
    public function getHeading(): string
    {
        return  __('filament-panels.question.fields.grade');
    }
    protected function getData(): array
    {
        $userId = Auth::user()->id;
        $tutor=Tutor::where('user_id',$userId)->first();

        $data = Question::where('tutor_id', $tutor->id)
            ->whereNotNull('answer_text')
            ->select('grade_id', DB::raw('count(*) as total'))
            ->groupBy('grade_id')
            ->with('grade')
            ->get();

        $labels = [];
        $values = [];

        foreach ($data as $row) {
            $labels[] = $row->grade->name ?? 'Unknown';
            $values[] = $row->total;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Questions Answered',
                    'data' => $values,
                    'backgroundColor' => [
                        '#4CAF50', // green
                        '#2196F3', // blue
                        '#FFC107', // yellow
                        '#F44336', // red
                        '#9C27B0', // purple
                        '#00BCD4', // cyan
                        '#FF5722', // deep orange
                        '#795548', // brown
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
