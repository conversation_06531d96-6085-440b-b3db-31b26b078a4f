<?php

namespace App\Filament\Widgets;

use App\Models\Customer; 
use Filament\Tables;
use Filament\Tables\Table; 
use Filament\Widgets\TableWidget as BaseWidget; 

/**
 * Class TopCustomers
 *
 * This Filament Table Widget displays a list of the top 10 customers.
 * It ranks customers based on the number of orders they have placed and their total spending.
 */
class TopCustomers extends BaseWidget
{
    /**
     * @var int|null The sort order for this widget in the Filament dashboard.
     * Lower numbers appear earlier.
     */
    
    protected static ?int $sort = 4;

    /**
     * Get the heading (title) for the table widget.
     *
     * @return string The translated heading for the table.
     */
    public function getHeading(): string
    {
        return __('filament-panels.chart.top_customers');
    }


    /**
     * Configures the table for the widget.
     *
     * @param Table $table The Filament Table instance to configure.
     * @return Table The configured Table instance.
     */
    public function table(Table $table): Table
    {
        return $table
            ->query(
                Customer::query()
                    ->withCount('orders')
                    ->withSum('orders as orders_sum_total', 'total_price')
                    ->orderByDesc('orders_count')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('العميل'),

                Tables\Columns\TextColumn::make('orders_count')
                    ->label('عدد الطلبات')
                    ->sortable(),

                // Total Purchases Column:
                Tables\Columns\TextColumn::make('orders_sum_total')
                    ->label('إجمالي المشتريات')
                    ->formatStateUsing(fn ($state) => number_format($state, 2) . ' د.ل')
                    ->sortable(),
            ]);
    }
}