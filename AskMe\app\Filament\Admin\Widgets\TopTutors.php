<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TutorActivities;
use App\Models\Tutor;
use App\Models\TutorActivity;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class TopTutors extends BaseWidget
{

    // Widget sort order on dashboard
    protected static ?int $sort = 8;

    // Full-width widget on dashboard
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'أفضل المعلمين (حسب عدد الإجابات)';

    protected function getHeading(): string
    {
        return __('filament-panels.dashboard.top_tutors');
    }

    protected function getTableQuery(): Builder
    {
        return Tutor::query()
            ->withCount(['questions as answered_count' => function ($query) {
                $query->whereNotNull('answer_text');
            }])
            ->with('user')
            ->orderByDesc('answered_count');
        // ->limit(10);
    }

    protected function getTableColumns(): array
    {
        return [
            TextColumn::make('user.name')
                ->label(__('filament-panels.dashboard.tutor_name'))->searchable()
                ->sortable(),

            TextColumn::make('answered_count')
                ->label(__('filament-panels.dashboard.questions_answered'))
                ->sortable()
                ->badge()
                ->color('success'),

            TextColumn::make('avg_answer_time')
                ->label(__('filament-panels.dashboard.avg_answer_time'))
                ->state(function ($record) {
                    $avgSeconds = TutorActivity::query()
                        ->where('tutor_activities.action', TutorActivities::Answer)
                        ->where('tutor_activities.tutor_id', $record->id)
                        ->join('questions', 'tutor_activities.question_id', '=', 'questions.id')
                        ->avg(DB::raw('TIMESTAMPDIFF(SECOND, questions.created_at, tutor_activities.created_at)'));

                    return $avgSeconds ? round($avgSeconds / 60, 1) : null;
                })
                ->formatStateUsing(fn($state) => $state ? $state . ' دقيقة' : '—')
                ->badge()
                ->color('gray'),
        ];
    }
}
