<?php

namespace App\Filament\Tutor\Widgets;

use App\Models\Question;
use App\Models\Tutor;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SubjectsBreakdownChart extends ChartWidget
{
    protected static ?string $heading = '';
    protected static ?int $sort = 2;


    public function getHeading(): string
    {
        return  __('filament-panels.question.fields.subject');
    }

    protected function getData(): array
    {
        $userId = Auth::user()->id;
        $tutor = Tutor::where('user_id', $userId)->first();

        // Group answered questions by subject
        $data = Question::where('tutor_id', $tutor->id)
            ->whereNotNull('answer_text')
            ->select('subject_id', DB::raw('count(*) as total'))
            ->groupBy('subject_id')
            ->with('subject')
            ->get();

        $labels = [];
        $values = [];

        foreach ($data as $row) {
            $labels[] = $row->subject->name ?? 'Unknown';
            $values[] = $row->total;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Questions Answered',
                    'data' => $values,
                    'backgroundColor' => [
                        '#6366F1',
                        '#06B6D4',
                        '#10B981',
                        '#84CC16',
                        '#F59E0B',
                        '#EF4444',
                        '#8B5CF6',
                        '#EC4899',
                        '#F97316',
                        '#3B82F6',

                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
