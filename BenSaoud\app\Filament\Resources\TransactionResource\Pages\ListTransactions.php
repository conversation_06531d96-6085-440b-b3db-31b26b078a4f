<?php

namespace App\Filament\Resources\TransactionResource\Pages;

use App\Filament\Resources\TransactionResource;
use App\Filament\Widgets\TransactionStatusOverview;
use App\Filament\Widgets\WhatsAppCustomerStats;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTransactions extends ListRecords
{
    protected static string $resource = TransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            

            // Actions\CreateAction::make(),
        ];
    }
    public function getHeaderWidgets(): array
{
    return [
        TransactionStatusOverview::class,
    ];
}
}
