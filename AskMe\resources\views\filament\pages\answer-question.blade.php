<x-filament::page>
    <div class="flex justify-center">
        <div class="w-full max-w-2xl space-y-6">
            {{-- grade and subject --}}
            <div class="p-4 border rounded shadow bg-white">
                {{-- Student Info --}}
                <div class="text-sm  mt-1">
                    {{ __('filament-panels.question.fields.name') }}:
                    <strong>{{ $question->student->name ?? 'Unknown' }}</strong>
                </div>
                {{-- grade Info --}}
                <div class="text-sm  mt-1">
                    <strong>{{ $question->grade->name ?? 'Unknown' }}</strong> -
                    <strong>{{ $question->subject->name ?? 'Unknown' }}</strong>
                </div>

                {{-- Timestamp --}}
                <div class="text-xs  mt-1">
                    {{ $question->created_at->format('Y-m-d H:i') }}
                </div>
            </div>

            {{-- Question Info --}}
            <div class="p-4 border rounded shadow bg-white">
                <h2 class="text-lg font-bold text-gray-800 mb-2"> {{__('filament-panels.question.fields.question_text')}}:</h2>
                <p class="text-gray-700">{{ $question->question_text }}</p>

                @if ($question->image_url)
                <div class="mt-4">
                    <img src="{{ asset($question->image_url) }}" class="w-full max-w-xs rounded shadow" alt="Question Image">
                </div>
                @endif
            </div>


            {{-- Uploaded Answer Preview --}}
            @if ($question->answer_url)
            <div class="p-4 border rounded shadow bg-white">
                <h2 class="text-md font-semibold text-gray-800 mb-2">{{__('filament-panels.question.fields.answer_text')}}:</h2>

                <p class="text-gray-700">{{ $question->answer_text }}</p>

                <h2 class="text-md font-semibold text-gray-800 mb-2">{{__('filament-panels.question.fields.uploaded_answer')}}:</h2>

                @php
                $fileUrl = asset($question->answer_url);
                $ext = strtolower(pathinfo($question->answer_url, PATHINFO_EXTENSION));
                @endphp

                @if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif']))
                <img src="{{ $fileUrl }}" alt="Answer Image" class="max-w-full rounded shadow">
                @elseif ($ext === 'mp4')
                <video controls class="w-full max-w-md rounded">
                    <source src="{{ $fileUrl }}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                @elseif ($ext === 'pdf')
                <a href="{{ $fileUrl }}" target="_blank" class="text-blue-600 underline">{{__('filament-panels.question.action.view')}}</a>
                @else
                <a href="{{ $fileUrl }}" target="_blank" class="text-blue-600 underline">{{__('filament-panels.question.action.download')}}</a>
                @endif
            </div>
            @endif

            {{-- Question comment --}}
            @if($question->comments->isNotEmpty())

            <div class="p-4 border rounded shadow bg-white">
                <h2 class="text-md font-semibold text-gray-800 mb-2">{{__('filament-panels.question.fields.comments')}}:</h2>
                @forelse ($question->comments as $comment)
                <p class="text-gray-700 mb-2">• {{ $comment->comment }}</p>
                @empty
                <p class="text-gray-400 italic">No comments yet.</p>
                @endforelse
            </div>
            @endif

            {{-- Answer Form --}}
            {{-- Toggleable Answer Form --}}
            <div x-data="{ open: false }">
                <x-filament::button
                    x-on:click="open = !open"
                    color="{{ $question->answer_text || $question->answer_url ? 'warning' : 'primary' }}"
                    class="mb-4">
                    <span x-show="!open">
                        {{ $question->answer_text || $question->answer_url ? __('filament-panels.question.action.edit_answer') : __('filament-panels.question.action.add_answer')}}
                    </span>
                    <span x-show="open">{{__('filament-panels.question.action.cancel') }}</span>
                </x-filament::button>

                <form
                    wire:submit.prevent="submit"
                    x-show="open"
                    x-transition
                    class="space-y-4 bg-white p-4 border rounded shadow">
                    {{ $this->form }}
                    <x-filament::button type="submit" color="success">
                        {{ __('filament-panels.question.action.save') }}
                    </x-filament::button>
                </form>
            </div>

        </div>
    </div>
</x-filament::page>