<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->uuid('student_id');
            $table->foreignId('tutor_id')->constrained()->nullable();
            $table->foreignId('subject_id')->constrained();
            $table->foreignId('grade_id')->constrained();
            $table->text('question_text');
            $table->string('image_url')->nullable();
            $table->string('image_id')->nullable(); 
            $table->text('answer_text')->nullable();
            $table->string('answer_id')->nullable(); 
            $table->string('answer_url')->nullable(); 
            $table->enum('answer_type',['Image','Video','Document'])->nullable();
            $table->timestamps();
        });
    }

    public function down(): void {
        Schema::dropIfExists('questions');
    }
};
