<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CategoryProduct extends Model
{
    
    protected $table='category_product';
    protected $fillable=['category_id','product_id'];

    public function products()
    {
        return $this->belongsToMany(Products::class);
    }
    public function categories()
{
    return $this->belongsToMany(Categories::class,'category_product', 'category_id', 'product_id');
}

}
