<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\QuestionsResource\Pages;
use App\Filament\Admin\Resources\QuestionsResource\RelationManagers;
use App\Models\Question;
use App\Models\Questions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Log;

class QuestionsResource extends Resource
{
    protected static ?string $model = Question::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';
    public static function getNavigationBadge(): ?string
{
    return static::getModel()::count();
}
    public function getTitle(): string
    {
        return __('filament-panels.question.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.question.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.question.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.question.title');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('#')
                    ->sortable()
                    ->searchable(),

                ImageColumn::make('image_url')
                    ->label(__('filament-panels.question.fields.question_image'))
                    ->getStateUsing(function ($record) {
                        $path = data_get($record, 'image_url');
                        return asset($path);
                    })
                    ->placeholder(
                        __('filament-panels.question.fields.no_image')
                    ),

                TextColumn::make('student.name')
                    ->label(__('filament-panels.question.fields.name'))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('question_text')
                    ->label(__('filament-panels.question.fields.question_text'))
                    ->searchable(),

                TextColumn::make('answer_text')
                    ->label(__('filament-panels.question.fields.answer_text'))
                    ->getStateUsing(function ($record) {
                        if ($record->answer_text === null && $record->answer_url === null) {
                            $answer = __('filament-panels.question.fields.not_answered');
                        } elseif ($record->answer_text === null) {
                            $answer = __('filament-panels.question.fields.no_answer_text');
                        } else {
                            $answer = $record->answer_text;
                        }
                        return $answer;
                    })->badge()
                    ->color(fn($record) => match (true) {
                        $record->answer_text === null && $record->answer_url === null => 'danger',
                        $record->answer_text === null => 'warning',
                        default => 'success',
                    }),

                TextColumn::make('created_at')
                    ->label(__('filament-panels.question.fields.created_at'))
                    ->dateTime()
                    ->sortable(),

                TextColumn::make('updated_at')
                    ->label(__('filament-panels.question.fields.updated_at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->with('tutor.tutorActivites');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('')
                    ->schema([
                        TextEntry::make('student.name')->label(__('filament-panels.tutor.fields.name')),

                        TextEntry::make('created_at')
                            ->label(__('filament-panels.question.fields.created_at'))
                            ->dateTime(),

                        TextEntry::make('question_text')->label(__('filament-panels.question.fields.question_text')),

                        ImageEntry::make('image_url')
                            ->label(__('filament-panels.question.fields.question_image'))
                            ->getStateUsing(function ($record) {
                                $path = data_get($record, 'image_url');
                                return asset($path);
                            })->hidden(fn($record) => empty($record->image_url)),
                    ]),
                Section::make('')
                    ->schema([
                        TextEntry::make('answer_text')
                            ->label(__('filament-panels.question.fields.answer_text'))
                            ->getStateUsing(function ($record) {
                                if (!$record->answer_text && !$record->answer_url) {
                                    return __('filament-panels.question.fields.not_answered');
                                } else {
                                    return $record->answer_text;
                                }
                            })->hidden(fn($record) => empty($record->answer_text)),

                        ViewEntry::make('answer_url')
                            ->label(__('filament-panels.question.fields.uploaded_answer'))
                            ->view('filament.infolists.media-entry')
                            ->getStateUsing(fn($record) => $record->answer_url ? asset($record->answer_url) : null)
                            ->hidden(fn($record) => empty($record->answer_url)),
                    ]),
                    
                Section::make('')
                    ->schema([
                        ViewEntry::make('tutorActivites')
                            ->label('Activity Log')
                            ->view('filament.infolists.tutor-activities-log')
                            ->columnSpanFull(),
                    ])
            ]);
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuestions::route('/'),
        ];
    }
}
