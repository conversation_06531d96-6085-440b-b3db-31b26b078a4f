<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TransactionStatus;
use App\Models\Transaction;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Log;

class TransactionStatusPieChart extends ChartWidget
{
    protected static ?string $heading =  'إحصائيات حالة المعاملات المدفوعة والمعلقة والفاشلة';
    protected static ?int $sort = 4;
    // Max chart height
    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        $statuses = TransactionStatus::cases();

        $labels = [];
        $data = [];
        $colors = [];

        foreach ($statuses as $status) {
            Log::info("st",[$status->value]);
            $count = Transaction::where('status', $status->value)->count();
            $labels[] = $status->label();
            $data[] = $count;
            $colors[] = $status->color();
        }

        return [
            'datasets' => [
                [
                    'label' => __('filament-panels.dashboard.transaction_status'),
                    'data' => $data,
                    'backgroundColor' => $colors,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
