<?php

namespace App\Enums;

enum TutorActivities : string
{
    case Answer = 'Answer';
    case Accept = 'Accept';
    case Edit = 'Edit';
    public function label(): string
    {
        return match($this) {
            self::Accept => __('filament-panels.tutor.activities.accept'),
            self::Answer => __('filament-panels.tutor.activities.answer'),
            self::Edit => __('filament-panels.tutor.activities.edit'),
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::Accept => 'info',
            self::Answer => 'success',
            self::Edit => 'warning',
        };
    }
 
}
