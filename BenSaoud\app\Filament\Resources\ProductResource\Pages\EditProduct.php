<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use App\Models\Products;
use App\Services\CatalogSyncService;
use Filament\Actions;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * EditProduct
 * 
 * Page component for editing products in the Filament admin panel.
 * Handles product updates with synchronization to the catalog service.
 */
class EditProduct extends EditRecord
{
    protected static string $resource = ProductResource::class;

    /**
     * Handle the record update process
     * Updates the product and synchronizes changes with the catalog service
     * 
     * @param Model $record The product record to update
     * @param array $data The new data for the product
     * @return Model The updated product record
     * @throws \Exception If catalog synchronization fails
     */
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        return DB::transaction(function () use ($record, $data) {
            // Update the product record with new data
            $record->update($data);
            $productId = $record->product_catelog_id;

            // Get only the fields that were changed in this update
            $changes = $record->getChanges();
            // Remove timestamp from changes as it's not needed for catalog sync
            unset($changes['updated_at']);

            // Sync changes to the catalog service
            $response = app(CatalogSyncService::class)->updateProduct($record, $changes, $productId);
            $responseJson = $response->json();

            // Handle catalog sync errors
            if (!isset($responseJson['success']) || $responseJson['success'] != true) {
                Log::error('Meta Catalog API Error:', $responseJson);
                throw new \Exception($responseJson ?? 'Failed to edit product.');
            }

            // Log successful sync
            Log::info("responseJson: ", [$responseJson]);
            return $record;
        });
    }

    /**
     * Define the header actions available on this page
     * Includes delete action with catalog synchronization
     * 
     * @return array Array of header actions
     */
    protected function getHeaderActions(): array
    {
        return [
            // Delete action with catalog sync after deletion
            Actions\DeleteAction::make()->after(function ($record) {
                // Log the deletion
                Log::info("Product deleted from table: " . $record->id);
                // Sync deletion with catalog service
                app(CatalogSyncService::class)->deleteProduct($record->product_catelog_id);
            }),
        ];
    }
}
