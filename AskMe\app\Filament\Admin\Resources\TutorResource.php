<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TutorResource\Pages;
use App\Filament\Admin\Resources\TutorResource\RelationManagers;
use App\Models\Grade;
use App\Models\Subject;
use App\Models\Tutor;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section as ComponentsSection;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Infolists\Components\BulletedListEntry;

class TutorResource extends Resource
{
    protected static ?string $model = Tutor::class;

    protected static ?string $navigationIcon = 'heroicon-s-user';

    public function getTitle(): string
    {
        return __('filament-panels.tutor.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.tutor.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.tutor.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.tutor.title');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Section for creating the related User record
                ComponentsSection::make('User Info')
                    ->schema([
                        TextInput::make('user.name')
                            ->label(__('filament-panels.tutor.fields.name'))
                            ->minLength(3)
                            ->maxLength(50)
                            ->required(),

                        TextInput::make('user.email')
                            ->label(__('filament-panels.tutor.fields.username'))
                            ->suffix('@askme.com')
                            ->minLength(3)
                            ->maxLength(100)
                            ->unique('users', 'email')
                            ->required(),

                        TextInput::make('phone')
                            ->label(__('filament-panels.tutor.fields.phone'))
                            ->unique('tutors', 'phone')
                            ->tel()
                            ->minLength(6)
                            ->maxLength(15)
                            ->required(),

                        TextInput::make('user.password')
                            ->label(__('filament-panels.tutor.fields.password'))
                            ->password()
                            ->minLength(4)
                            ->maxLength(20)
                            ->dehydrated(false),

                    ])->hiddenOn('edit'),
                // Section for assigning subjects and grades to the tutor
                ComponentsSection::make(__('filament-panels.tutor.fields.grade_subject'))
                    ->schema([
                        Repeater::make('tutorSubjects')
                            ->relationship()
                            ->schema([
                                Select::make('subject_id')
                                    ->label(__('filament-panels.subject.singular'))
                                    ->options(Subject::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),


                                Select::make('grade_id')
                                    ->label(__('filament-panels.grade.singular'))
                                    ->options(Grade::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                            ])
                            ->columns(2)
                            ->collapsible(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')->label(__('filament-panels.tutor.fields.name'))->sortable()->searchable(),
                TextColumn::make('phone')->label(__('filament-panels.tutor.fields.phone'))->sortable()->searchable(),
                IconColumn::make('user.is_active')
                    ->label(__('filament-panels.tutor.fields.status'))
                    ->boolean(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make(__('filament-panels.tutor.fields.info'))
                    ->schema([
                        TextEntry::make('user.name')->label(__('filament-panels.tutor.fields.name')),
                        TextEntry::make('phone')->label(__('filament-panels.tutor.fields.phone')),
                        TextEntry::make('user.is_active')
                            ->label(__('filament-panels.tutor.fields.status'))
                            ->formatStateUsing(fn($state) => $state ? '✅ ' . (__('filament-panels.tutor.fields.active')) : '❌ ' . (__('filament-panels.tutor.fields.disabled'))),
                    ]),

                Section::make((__('filament-panels.tutor.fields.grade_subject')))
                    ->schema([
                        TextEntry::make('')
                            ->state(function ($record) {
                                return '<ul style="padding-left: 1.2rem; list-style: disc;">' . $record->tutorSubjects->map(function ($ts) {
                                    return "<li>{$ts->subject->name} (Grade: {$ts->grade->name})</li>";
                                })->join('') . '</ul>';
                            })
                            ->html(),
                    ]),
                Section::make('Statistics')
                    ->schema([
                        TextEntry::make('questions_count')
                            ->label('Total Questions')
                            ->default(fn($record) => $record->questions()->count()),
                        TextEntry::make('subjects_count')
                            ->label('Total Subjects')
                            ->default(fn($record) => $record->tutorSubjects()->count()),
                    ])
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTutors::route('/'),
            'create' => Pages\CreateTutor::route('/create'),
            'edit' => Pages\EditTutor::route('/{record}/edit'),
        ];
    }
}
