<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Jobs\SendPushNotificationJob;
use App\Models\FirebaseToken;
use  App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class LanguageController extends Controller

{
    public function switchLang(string $locale): RedirectResponse
    {
        if (! in_array($locale, ['en', 'ar'])) {
            abort(400);
        }
        $user = Auth::user();
        if ($user instanceof User) {
            $user->language = $locale;
            $user->save();
        }
        return back();
    }
}
