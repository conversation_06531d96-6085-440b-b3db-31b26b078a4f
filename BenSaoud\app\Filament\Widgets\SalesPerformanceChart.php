<?php

namespace App\Filament\Widgets;

use App\Models\Orders;
use Carbon\Carbon;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;
use Illuminate\Support\Facades\DB;

class SalesPerformanceChart extends ApexChartWidget
{
    // Unique identifier for the chart instance
    protected static ?string $chartId = 'salesPerformance';

    // Widget spans full width in the dashboard
    protected int | string | array $columnSpan = 'full';

    // Widget display order in dashboard
    protected static ?int $sort = 2;

    // Maximum height constraint for the chart
    protected static ?string $maxHeight = '400px';

    // Default time period filter
    public ?string $filter = 'month';

    // Get the translated heading for the widget
    public function getHeading(): string
    {
        return __('filament-panels.chart.sales_performance_analysis');
    }

    // Define available time period filters
    protected function getFilters(): ?array
    {
        return [
            'month' => __('filament-panels.chart.monthly_analysis'),
            'quarter' => __('filament-panels.chart.quarterly_analysis'),
            'year' => __('filament-panels.chart.yearly_analysis'),
        ];
    }

    // Return chart options based on selected time period
    protected function getOptions(): array
    {
        $filter = $this->filter ?? 'month';
        
        if ($filter === 'month') {
            return $this->getMonthlyAnalysis();
        } elseif ($filter === 'quarter') {
            return $this->getQuarterlyAnalysis();
        } else {
            return $this->getYearlyAnalysis();
        }
    }

    // Generate monthly sales analysis chart
    protected function getMonthlyAnalysis(): array
    {
        $currentYear = Carbon::now()->year;
        $currentMonth = Carbon::now()->month;

        // Get daily sales data for current month
        $currentMonthData = Orders::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count'),         // Daily order count
            DB::raw('COALESCE(SUM(total_price), 0) as revenue')  // Daily revenue with null handling
        )
            ->whereYear('created_at', $currentYear)
            ->whereMonth('created_at', $currentMonth)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Get daily sales data for previous month for comparison
        $lastMonthData = Orders::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count'),
            DB::raw('COALESCE(SUM(total_price), 0) as revenue')
        )
            ->whereYear('created_at', Carbon::now()->subMonth()->year)
            ->whereMonth('created_at', Carbon::now()->subMonth()->month)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'chart' => [
                'type' => 'line',           // Line chart for trend visualization
                'height' => 300,            // Fixed height
                'toolbar' => [
                    'show' => true,         // Enable chart toolbar
                ],
                'zoom' => [
                    'enabled' => true,      // Enable zoom functionality
                ],
            ],
            'series' => [
                [
                    'name' => __('filament-panels.chart.current_month_revenue'),
                    'data' => $currentMonthData->pluck('revenue')->toArray(),
                ],
                [
                    'name' => __('filament-panels.chart.previous_month_revenue'),
                    'data' => $lastMonthData->pluck('revenue')->toArray(),
                ],
                [
                    'name' => __('filament-panels.chart.current_month_orders'),
                    'data' => $currentMonthData->pluck('count')->toArray(),
                ],
            ],
            'xaxis' => [
                'categories' => $currentMonthData->pluck('date')->map(function ($date) {
                    return Carbon::parse($date)->translatedFormat('d M');  // Format: "01 Jan"
                })->toArray(),
                'labels' => [
                    'rotate' => -45,        // Angle labels for better readability
                ],
            ],
            'yaxis' => [
                [
                    'title' => [
                        'text' => __('filament-panels.chart.revenue_lyd'),  // Left Y-axis: Revenue
                    ],
                    'decimalsInFloat' => 2,
                ],
                [
                    'opposite' => true,     // Place axis on right side
                    'title' => [
                        'text' => __('filament-panels.chart.number_of_orders'),  // Right Y-axis: Orders
                    ],
                ],
            ],
            'stroke' => [
                'curve' => 'smooth',        // Smooth line curves
                'width' => [2, 2, 1],       // Line thickness for each series
            ],
            'colors' => ['rgb(245, 208, 23)', '#6B7280', '#3B82F6'],  // Yellow, Gray, Blue
            'title' => [
                'text' => __('filament-panels.chart.monthly_sales'),
                'align' => 'center',
            ],
            'legend' => [
                'position' => 'top',        // Legend position
            ],
            'annotations' => [
                'yaxis' => [
                    [
                        'y' => $currentMonthData->avg('revenue'),
                        'borderColor' =>'rgb(245, 208, 23)',  // Average line color
                        'label' => [
                            'text' => 'Avg Revenue: ' . number_format($currentMonthData->avg('revenue'), 2) . ' LYD',
                        ],
                    ],
                ],
            ],
        ];
    }

    // Generate quarterly sales analysis chart
    protected function getQuarterlyAnalysis(): array
    {
        $currentQuarter = Carbon::now()->quarter;
        $currentYear = Carbon::now()->year;

        // Current quarter monthly data
        $currentQuarterData = Orders::select(
            DB::raw('MONTH(created_at) as month'),
            DB::raw('COUNT(*) as count'),
            DB::raw('COALESCE(SUM(total_price), 0) as revenue')
        )
            ->whereYear('created_at', $currentYear)
            ->whereRaw('QUARTER(created_at) = ?', [$currentQuarter])
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Previous quarter data
        $lastQuarterData = Orders::select(
            DB::raw('MONTH(created_at) as month'),
            DB::raw('COUNT(*) as count'),
            DB::raw('COALESCE(SUM(total_price), 0) as revenue')
        )
            ->whereYear('created_at', Carbon::now()->subQuarter()->year)
            ->whereRaw('QUARTER(created_at) = ?', [Carbon::now()->subQuarter()->quarter])
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Calculate quarter-over-quarter growth
        $currentQuarterTotal = $currentQuarterData->sum('revenue');
        $lastQuarterTotal = $lastQuarterData->sum('revenue');
        $qoqGrowth = $lastQuarterTotal > 0 
            ? (($currentQuarterTotal - $lastQuarterTotal) / $lastQuarterTotal) * 100 
            : 0;

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 300,
                'toolbar' => ['show' => true],
            ],
            'series' => [
                [
                    'name' => __('filament-panels.chart.current_quarter_revenue'),
                    'data' => $currentQuarterData->pluck('revenue')->toArray(),
                ],
                [
                    'name' => __('filament-panels.chart.previous_quarter_revenue'),
                    'data' => $lastQuarterData->pluck('revenue')->toArray(),
                ],
            ],
            'xaxis' => [
                'categories' => $currentQuarterData->pluck('month')->map(function ($month) {
                    return Carbon::create()->month($month)->translatedFormat('F');
                })->toArray(),
            ],
            'yaxis' => [
                'title' => [
                    'text' => __('filament-panels.chart.revenue_lyd'),
                ],
                'decimalsInFloat' => 2,
            ],
            'colors' => ['rgb(245, 208, 23)', '#6B7280'],
            'title' => [
                'text' => __('filament-panels.chart.quarterly_comparison'),
                'align' => 'center',
            ],
            'plotOptions' => [
                'bar' => [
                    'horizontal' => false,
                    'columnWidth' => '55%',
                    'endingShape' => 'rounded',
                ],
            ],
        ];
    }

    // Generate yearly sales analysis chart
    protected function getYearlyAnalysis(): array
    {
        // Get current year for comparison
        $currentYear = Carbon::now()->year;

        // Fetch current year's monthly data with revenue and order counts
        // Using COALESCE to handle NULL values and return 0 instead
        $currentYearData = Orders::select(
            DB::raw('MONTH(created_at) as month'),
            DB::raw('COUNT(*) as count'),  // Count of orders per month
            DB::raw('COALESCE(SUM(total_price), 0) as revenue')  // Total revenue per month
        )
            ->whereYear('created_at', $currentYear)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Fetch previous year's data for comparison
        $lastYearData = Orders::select(
            DB::raw('MONTH(created_at) as month'),
            DB::raw('COUNT(*) as count'),
            DB::raw('COALESCE(SUM(total_price), 0) as revenue')
        )
            ->whereYear('created_at', $currentYear - 1)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Create a collection of all 12 months
        $months = collect(range(1, 12));

        // Fill in missing months with zero values for current year
        // This ensures we have data points for all months even if no orders existed
        $currentYearData = $months->map(function ($month) use ($currentYearData) {
            $monthData = $currentYearData->firstWhere('month', $month);
            return [
                'month' => $month,
                'revenue' => $monthData ? $monthData->revenue : 0,
                'count' => $monthData ? $monthData->count : 0,
            ];
        });

        // Fill in missing months with zero values for previous year
        $lastYearData = $months->map(function ($month) use ($lastYearData) {
            $monthData = $lastYearData->firstWhere('month', $month);
            return [
                'month' => $month,
                'revenue' => $monthData ? $monthData->revenue : 0,
                'count' => $monthData ? $monthData->count : 0,
            ];
        });

        return [
            // Basic chart configuration
            'chart' => [
                'type' => 'bar',  // Set chart type to bar
                'height' => 300,   // Chart height
                'toolbar' => ['show' => true],  // Show the chart toolbar
                'stacked' => false,  // Disable stacking of bars
            ],
            // Define the data series to be displayed
            'series' => [
                [
                    'name' => __('filament-panels.chart.revenue_for_year', ['year' => $currentYear]),
                    'data' => $currentYearData->pluck('revenue')->toArray(),
                    'type' => 'column',  // Display as columns
                ],
                [
                    'name' => __('filament-panels.chart.revenue_for_year', ['year' => $currentYear - 1]),
                    'data' => $lastYearData->pluck('revenue')->toArray(),
                    'type' => 'column',  // Display as columns
                ],
                [
                    'name' => __('filament-panels.chart.number_of_orders') . ' ' . $currentYear,
                    'data' => $currentYearData->pluck('count')->toArray(),
                    'type' => 'column',  // Display as columns
                ],
            ],
            // Configure the appearance of the bars
            'plotOptions' => [
                'bar' => [
                    'horizontal' => false,  // Vertical bars
                    'columnWidth' => '55%',  // Width of the bars
                    'endingShape' => 'rounded',  // Rounded corners
                    'borderRadius' => 4,  // Border radius size
                ],
            ],
            // X-axis configuration
            'xaxis' => [
                'categories' => $months->map(function ($month) {
                    return Carbon::create()->month($month)->translatedFormat('M');  // Short month names
                })->toArray(),
            ],
            // Y-axis configuration with dual axes
            'yaxis' => [
                [
                    'title' => [
                        'text' => __('filament-panels.chart.revenue_lyd'),  // Revenue axis title
                    ],
                    'decimalsInFloat' => 2,  // Show 2 decimal places
                ],
                [
                    'opposite' => true,  // Place on right side
                    'title' => [
                        'text' => __('filament-panels.chart.number_of_orders'),  // Orders axis title
                    ],
                ],
            ],
            // Color scheme for different series
            'colors' => ['rgb(245, 208, 23)', '#6B7280', '#3B82F6'],  // Green, Gray, Blue
            // Chart title
            'title' => [
                'text' => __('filament-panels.chart.yearly_revenue'),
                'align' => 'center',
            ],
            // Disable data labels on the bars
            'dataLabels' => [
                'enabled' => false,
            ],
            // Configure tooltips
            'tooltip' => [
                'shared' => true,  // Show all series in tooltip
                'intersect' => false,  // Show tooltip without intersection
                'y' => [
                    [
                        'formatter' => 'function (value) {
                            return value.toFixed(2) + " LYD";  // Format revenue
                        }',
                    ],
                    [
                        'formatter' => 'function (value) {
                            return value.toFixed(2) + " LYD";  // Format revenue
                        }',
                    ],
                    [
                        'formatter' => 'function (value) {
                            return value + " Orders";  // Format order count
                        }',
                    ],
                ],
            ],
            // Legend configuration
            'legend' => [
                'position' => 'top',  // Place legend at top
            ],
        ];
    }
} 