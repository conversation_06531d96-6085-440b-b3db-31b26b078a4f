<?php

namespace App\Http\Controllers;

use App\Models\OrderItems;
use App\Models\Products;
use App\Models\Categories;
use App\Models\Orders;
use App\Models\Customer;
use App\Models\Brands;
use Barryvdh\DomPDF\Facade\Pdf;
use Mpdf\Mpdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Generates a PDF report for sales and revenue.
     *
     * @return \Illuminate\Http\Response
     */
    public function generateSalesRevenuePdf()
    {
        $now = Carbon::now();
        $lastMonth = $now->copy()->subMonth();
        $thisMonth = $now->copy()->startOfMonth();

        // Calculate total revenue and its change percentage
        $totalRevenue = OrderItems::sum(DB::raw('price * quantity'));
        $lastPeriodRevenue = OrderItems::where('created_at', '<', $thisMonth)
            ->where('created_at', '>=', $lastMonth)
            ->sum(DB::raw('price * quantity'));
        $revenueChange = $lastPeriodRevenue > 0 ? 
            round((($totalRevenue - $lastPeriodRevenue) / $lastPeriodRevenue) * 100) : 0;

        // Calculate total sales units and its change
        $totalSales = OrderItems::sum('quantity');
        $lastPeriodSales = OrderItems::where('created_at', '<', $thisMonth)
            ->where('created_at', '>=', $lastMonth)
            ->sum('quantity');
        $salesChange = $lastPeriodSales > 0 ? 
            round((($totalSales - $lastPeriodSales) / $lastPeriodSales) * 100) : 0;

        // Calculate average order value
        $totalOrders = Orders::count();
        $avgOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;
        $lastPeriodAvgOrder = Orders::where('created_at', '<', $thisMonth)
            ->where('created_at', '>=', $lastMonth)
            ->avg(DB::raw('total_price'));
        $avgOrderChange = $lastPeriodAvgOrder > 0 ? 
            round((($avgOrderValue - $lastPeriodAvgOrder) / $lastPeriodAvgOrder) * 100) : 0;

        // Calculate total customers and growth
        $totalCustomers = Customer::count();
        $newCustomers = Customer::where('created_at', '>=', $thisMonth)->count();
        $lastMonthCustomers = Customer::where('created_at', '<', $thisMonth)
            ->where('created_at', '>=', $lastMonth)
            ->count();
        $customerGrowth = $lastMonthCustomers > 0 ? 
            round((($newCustomers - $lastMonthCustomers) / $lastMonthCustomers) * 100) : 0;

        // Calculate repeat purchase rate
        $repeatCustomers = Orders::select('customer_id')
            ->groupBy('customer_id')
            ->havingRaw('COUNT(*) > 1')
            ->count();
        $repeatRate = $totalCustomers > 0 ? round(($repeatCustomers / $totalCustomers) * 100) : 0;

        // Calculate average items per order
        $avgItemsPerOrder = $totalOrders > 0 ? round($totalSales / $totalOrders, 2) : 0;
        $lastPeriodAvgItems = OrderItems::where('created_at', '<', $thisMonth)
            ->where('created_at', '>=', $lastMonth)
            ->avg('quantity');
        $avgItemsChange = $lastPeriodAvgItems > 0 ? 
            round((($avgItemsPerOrder - $lastPeriodAvgItems) / $lastPeriodAvgItems) * 100) : 0;

        // Get product sales by category
        $productSales = Categories::select(
            'categories.name as category',
            DB::raw('SUM(order_items.quantity) as sales'),
            DB::raw('SUM(order_items.price * order_items.quantity) as revenue')
        )
        ->join('category_product', 'categories.id', '=', 'category_product.category_id')
        ->join('products', 'category_product.product_id', '=', 'products.id')
        ->join('order_items', 'products.id', '=', 'order_items.product_id')
        ->groupBy('categories.name')
        ->get()
        ->toArray();

        // Get top products
        $topProducts = Products::select(
            'products.name',
            DB::raw('SUM(order_items.quantity) as unitsSold'),
            DB::raw('SUM(order_items.price * order_items.quantity) as revenue')
        )
        ->join('order_items', 'products.id', '=', 'order_items.product_id')
        ->groupBy('products.id', 'products.name')
        ->orderBy('revenue', 'desc')
        ->limit(4)
        ->get()
        ->toArray();

        $reportData = [
            'title' => 'تقرير المبيعات والإيرادات',
            'date' => $now->format('F j, Y'),
            'summary' => 'يقدم هذا التقرير نظرة شاملة عن المبيعات والإيرادات ومقاييس العملاء للفترة الحالية.',
            'metrics' => [
                [
                    'name' => 'إجمالي الإيرادات', 
                    'value' => '$' . number_format($totalRevenue, 2), 
                    'change' => ($revenueChange >= 0 ? '+' : '') . $revenueChange . '%',
                    'trend' => $revenueChange >= 0 ? 'up' : 'down'
                ],
                [
                    'name' => 'إجمالي وحدات المبيعات', 
                    'value' => number_format($totalSales), 
                    'change' => ($salesChange >= 0 ? '+' : '') . $salesChange . '%',
                    'trend' => $salesChange >= 0 ? 'up' : 'down'
                ],
                [
                    'name' => 'متوسط قيمة الطلب', 
                    'value' => '$' . number_format($avgOrderValue, 2), 
                    'change' => ($avgOrderChange >= 0 ? '+' : '') . $avgOrderChange . '%',
                    'trend' => $avgOrderChange >= 0 ? 'up' : 'down'
                ],
                [
                    'name' => 'إجمالي العملاء',
                    'value' => number_format($totalCustomers),
                    'change' => ($customerGrowth >= 0 ? '+' : '') . $customerGrowth . '%',
                    'trend' => $customerGrowth >= 0 ? 'up' : 'down'
                ],
                [
                    'name' => 'عملاء جدد هذا الشهر',
                    'value' => number_format($newCustomers),
                    'change' => 'عملاء جدد',
                    'trend' => 'neutral'
                ],
                [
                    'name' => 'معدل تكرار الشراء',
                    'value' => $repeatRate . '%',
                    'change' => 'من إجمالي العملاء',
                    'trend' => 'neutral'
                ],
                [
                    'name' => 'متوسط المنتجات لكل طلب',
                    'value' => number_format($avgItemsPerOrder, 1),
                    'change' => ($avgItemsChange >= 0 ? '+' : '') . $avgItemsChange . '%',
                    'trend' => $avgItemsChange >= 0 ? 'up' : 'down'
                ]
            ],
            'productSales' => $productSales,
            'topProducts' => $topProducts
        ];

        // Configure mPDF with Arabic support
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font' => 'dejavusanscondensed',
            'default_font_size' => 12,
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 16,
            'margin_bottom' => 16,
            'margin_header' => 9,
            'margin_footer' => 9,
            'orientation' => 'P',
            'tempDir' => storage_path('app/public'),
            'useOTL' => 0xFF,    // Use OpenType Layout features
            'useKashida' => 75,  // Use kashida for text justification in Arabic
        ]);

        // Set document direction to RTL
        $mpdf->SetDirectionality('rtl');
        
        // Generate HTML content
        $html = view('reports.sales_revenue_pdf', compact('reportData'))->render();
        
        // Write the HTML to the PDF
        $mpdf->WriteHTML($html);

        // Output the PDF
        return response($mpdf->Output('sales_revenue_report_' . now()->format('Ymd_His') . '.pdf', 'D'), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="sales_revenue_report_' . now()->format('Ymd_His') . '.pdf"'
        ]);
    }

    /**
     * Generates a PDF report for inventory status.
     *
     * @return \Illuminate\Http\Response
     */
    public function generateInventoryPdf()
    {
        $now = Carbon::now();

        // Get products with their inventory status
        $products = Products::select(
            'products.name',
            'products.code',
            'products.quantity as stock_quantity',
            'products.price',
            'products.brand',
            DB::raw('(SELECT SUM(quantity) FROM order_items WHERE product_id = products.id) as total_sold')
        )
        ->with('categories') // Eager load categories
        ->get();

        // Calculate inventory metrics
        $totalProducts = $products->count();
        $totalStock = $products->sum('stock_quantity');
        $lowStockThreshold = 10; // You can adjust this value
        $lowStockCount = $products->where('stock_quantity', '<=', $lowStockThreshold)->count();
        $outOfStockCount = $products->where('stock_quantity', '<=', 0)->count();

        // Calculate total inventory value
        $totalValue = $products->sum(function($product) {
            return $product->stock_quantity * $product->price;
        });

        // Group products by category for analysis
        $categoryStock = [];
        foreach ($products as $product) {
            foreach ($product->categories as $category) {
                if (!isset($categoryStock[$category->name])) {
                    $categoryStock[$category->name] = [
                        'total_items' => 0,
                        'total_value' => 0,
                        'low_stock' => 0,
                        'sales' => 0,
                        'revenue' => 0
                    ];
                }
                $categoryStock[$category->name]['total_items'] += $product->stock_quantity;
                $categoryStock[$category->name]['total_value'] += ($product->stock_quantity * $product->price);
                $categoryStock[$category->name]['sales'] += ($product->total_sold ?? 0);
                $categoryStock[$category->name]['revenue'] += ($product->total_sold ?? 0) * $product->price;
                if ($product->stock_quantity <= $lowStockThreshold) {
                    $categoryStock[$category->name]['low_stock']++;
                }
            }
        }

        $reportData = [
            'title' => 'تقرير حالة المخزون',
            'date' => $now->format('F j, Y'),
            'summary' => 'تقرير شامل عن حالة المخزون الحالي، بما في ذلك المنتجات المتوفرة والمنتجات التي تحتاج إلى إعادة الطلب.',
            'metrics' => [
                [
                    'name' => 'إجمالي المنتجات',
                    'value' => number_format($totalProducts),
                    'change' => 'منتج مختلف',
                    'trend' => 'neutral'
                ],
                [
                    'name' => 'إجمالي المخزون',
                    'value' => number_format($totalStock),
                    'change' => 'وحدة متوفرة',
                    'trend' => 'neutral'
                ],
                [
                    'name' => 'قيمة المخزون',
                    'value' => '$' . number_format($totalValue, 2),
                    'change' => 'القيمة الإجمالية',
                    'trend' => 'neutral'
                ],
                [
                    'name' => 'المنتجات منخفضة المخزون',
                    'value' => number_format($lowStockCount),
                    'change' => 'تحتاج إعادة طلب',
                    'trend' => 'down'
                ],
                [
                    'name' => 'المنتجات غير المتوفرة',
                    'value' => number_format($outOfStockCount),
                    'change' => 'نفذت من المخزون',
                    'trend' => 'down'
                ]
            ],
            'categoryAnalysis' => collect($categoryStock)->map(function($data, $category) {
                return [
                    'category' => $category,
                    'total_items' => $data['total_items'],
                    'total_value' => $data['total_value'],
                    'low_stock_items' => $data['low_stock']
                ];
            })->values()->toArray(),
            'productSales' => collect($categoryStock)->map(function($data, $category) {
                return [
                    'category' => $category,
                    'sales' => $data['sales'],
                    'revenue' => $data['revenue']
                ];
            })->values()->toArray(),
            'products' => $products->map(function($product) use ($lowStockThreshold) {
                return [
                    'name' => $product->name,
                    'code' => $product->code,
                    'brand' => $product->brand ?? 'غير محدد',
                    'category' => $product->categories->pluck('name')->join(', '),
                    'stock' => $product->stock_quantity,
                    'price' => $product->price,
                    'value' => $product->stock_quantity * $product->price,
                    'total_sold' => $product->total_sold ?? 0,
                    'status' => $product->stock_quantity <= 0 ? 'نفذ من المخزون' : 
                              ($product->stock_quantity <= $lowStockThreshold ? 'مخزون منخفض' : 'متوفر')
                ];
            })->toArray()
        ];

        // Configure mPDF with Arabic support
        $mpdf = new Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font' => 'dejavusanscondensed',
            'default_font_size' => 12,
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 16,
            'margin_bottom' => 16,
            'margin_header' => 9,
            'margin_footer' => 9,
            'orientation' => 'P',
            'tempDir' => storage_path('app/public'),
            'useOTL' => 0xFF,    // Use OpenType Layout features
            'useKashida' => 75,  // Use kashida for text justification in Arabic
        ]);

        // Set document direction to RTL
        $mpdf->SetDirectionality('rtl');
        
        // Generate HTML content
        $html = view('reports.inventory_pdf', compact('reportData'))->render();
        
        // Write the HTML to the PDF
        $mpdf->WriteHTML($html);

        // Output the PDF
        return response($mpdf->Output('inventory_report_' . now()->format('Ymd_His') . '.pdf', 'D'), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="inventory_report_' . now()->format('Ymd_His') . '.pdf"'
        ]);
    }
}
