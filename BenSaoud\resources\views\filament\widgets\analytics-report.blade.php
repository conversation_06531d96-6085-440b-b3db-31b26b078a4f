<x-filament::widget>
    <x-filament::card>
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium">{{ __('filament-panels.chart.analysis_report.title') }}</h2>
        </div>

        <form wire:submit="generateReport" class="mt-4 space-y-4">
            <!-- Report Type Selection -->
            <div class="grid grid-cols-1 gap-4">
                <select
                    wire:model="reportType"
                    class="block w-full border-gray-300 rounded-lg shadow-sm focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                >
                    @foreach($reportTypes as $value => $label)
                        <option value="{{ $value }}">{{ __($label) }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Date Range Selection -->
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ __('Start Date') }}</label>
                    <input 
                        type="date" 
                        wire:model="startDate"
                        min="{{ now()->subYear()->format('Y-m-d') }}"
                        max="{{ now()->format('Y-m-d') }}"
                        class="block w-full mt-1 border-gray-300 rounded-lg shadow-sm focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                    >
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ __('End Date') }}</label>
                    <input 
                        type="date" 
                        wire:model="endDate"
                        min="{{ $startDate }}"
                        max="{{ now()->format('Y-m-d') }}"
                        @if($reportType !== 'custom') disabled @endif
                        class="block w-full mt-1 border-gray-300 rounded-lg shadow-sm focus:border-primary-500 focus:ring-1 focus:ring-primary-500 disabled:bg-gray-100"
                    >
                </div>
            </div>

            <!-- Generate Report Button -->
            <div class="flex justify-end">
                <button
                    type="submit"
                    class="inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white transition-colors bg-primary-600 border border-transparent rounded-lg hover:bg-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                >
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
                    </svg>
                    {{ __('filament-panels.chart.analysis_report.download') }}
                </button>
            </div>
        </form>
    </x-filament::card>
</x-filament::widget> 