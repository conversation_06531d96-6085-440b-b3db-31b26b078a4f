<?php

namespace App\Filament\Widgets;

use App\Enums\TransactionStatus; 
use App\Models\WhatsappTransactions; 
use Filament\Widgets\ChartWidget; 

/**
 * Class TransactionStatusPie
 *
 * This Filament Chart Widget displays a pie chart showing the distribution
 * of transaction statuses from the WhatsappTransactions model.
 */
class TransactionStatusPie extends ChartWidget
{
    /**
     * @var int|null The sort order for this widget in the Filament dashboard.
     * Lower numbers appear earlier.
     */
    protected static ?int $sort = 3;

    /**
     * @var string|null The maximum height of the chart container.
     * This helps control the widget's layout on the dashboard.
     */
    protected static ?string $maxHeight = '300px';

    /**
     * Get the heading (title) for the chart widget.
     *
     * @return string The translated heading for the chart.
     */
    public function getHeading(): string
    {
        return __('filament-panels.chart.transaction_status');
    }

    /**
     * Prepares the data for the chart.
     * This method fetches transaction status counts and formats them for Chart.js.
     *
     * @return array An associative array containing 'labels' and 'datasets' for the chart.
     */
    protected function getData(): array
    {
        // Retrieve all possible transaction statuses defined in the TransactionStatus Enum.
        $statuses = collect(TransactionStatus::cases());

        // Query the WhatsappTransactions table to get the count of transactions
        // for each unique status.
        $counts = WhatsappTransactions::query()
            ->selectRaw('status, COUNT(*) as total') // Select the status and its total count
            ->groupBy('status') // Group the results by status
            ->pluck('total', 'status'); // Convert the result into a key-value pair collection
                                        // where status is the key and total count is the value.

        // Initialize arrays to hold the chart's labels, data values, and background colors.
        $labels = [];
        $data = [];
        $colors = [];
        // Get the current application locale to determine if Arabic labels are needed.
        $locale = app()->getLocale();

        // Iterate through each defined transaction status.
        foreach ($statuses as $status) {
            // Determine the label for the current status.
            // If the locale is 'ar' (Arabic), use the localized label from the Enum's `label()` method.
            // Otherwise, use the raw enum value (e.g., 'pending', 'completed').
            $label = $locale === 'ar'
                ? $status->label()
                : $status->value;

            // Get the count for the current status from the $counts collection.
            // If a status has no transactions, default its count to 0.
            $count = $counts[$status->value] ?? 0;

            // Assign a specific RGB color based on the transaction status.
            // This provides a consistent visual representation for each status type.
            $color = match ($status) {
                TransactionStatus::PENDING   => 'rgb(255, 193, 7)',   // Yellow for pending
                TransactionStatus::COMPLETED => 'rgb(76, 175, 80)',  // Green for completed
                TransactionStatus::FAILED    => 'rgb(244, 67, 54)',  // Red for failed
                TransactionStatus::CANCELED  => 'rgb(158, 158, 158)',// Grey for canceled
                default                      => 'rgb(0, 0, 0)',      // Black for any unhandled status
            };

            // Only add data to the chart if the count for that status is greater than 0.
            // This prevents displaying slices for statuses with no transactions.
            if ($count > 0) {
                $labels[] = $label;   // Add the determined label
                $data[] = $count;     // Add the transaction count
                $colors[] = $color;   // Add the assigned color
            }
        }

        // Return the structured data for Chart.js.
        return [
            'labels' => $labels, 
            'datasets' => [     
                [
                    'label' => __('filament-panels.chart.transaction_status'), 
                    'data' => $data,               // Array of numerical values (counts)
                    'backgroundColor' => $colors,  // Array of colors corresponding to each data point
                ],
            ],
        ];
    }

    /**
     * Defines the type of chart to be rendered.
     *
     * @return string The chart type
     */
    protected function getType(): string
    {
        return 'pie';
    }
}