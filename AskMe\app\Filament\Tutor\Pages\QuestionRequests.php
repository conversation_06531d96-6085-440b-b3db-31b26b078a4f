<?php

namespace App\Filament\Tutor\Pages;

use App\Models\Question;
use App\Models\Tutor;
use App\Models\TutorSubject;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class QuestionRequests extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.question-requests';
    protected static ?string $title = '';
    protected static bool $shouldRegisterNavigation = true;

    public static function getNavigationBadge(): ?string
    {
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');
        $tutorSubjects = TutorSubject::where('tutor_id', $tutorId)
            ->get(['grade_id', 'subject_id']);

        $questions = Question::whereNull('tutor_id')
            ->where(function ($query) use ($tutorSubjects) {
                foreach ($tutorSubjects as $subject) {
                    $query->orWhere(function ($q) use ($subject) {
                        $q->where('grade_id', $subject->grade_id)
                            ->where('subject_id', $subject->subject_id);
                    });
                }
            })
            ->latest()
            ->count();
        return $questions;
    }
    public function getTitle(): string
    {
        return __('filament-panels.question_request.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.question_request.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.question_request.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.question_request.title');
    }
    public function getViewData(): array
    {
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');
        $tutorSubjects = TutorSubject::where('tutor_id', $tutorId)
            ->get(['grade_id', 'subject_id']);

        $questions = Question::whereNull('tutor_id')
            ->where(function ($query) use ($tutorSubjects) {
                foreach ($tutorSubjects as $subject) {
                    $query->orWhere(function ($q) use ($subject) {
                        $q->where('grade_id', $subject->grade_id)
                            ->where('subject_id', $subject->subject_id);
                    });
                }
            })
            ->latest()
            ->get();
        return [
            'questions' => $questions,
        ];
    }

    public function acceptQuestion(int $questionId)
    {
        $question = Question::findOrFail($questionId);

        if ($question->tutor_id !== null) {
            Notification::make()
                ->title('This question was already accepted by another tutor.')
                ->danger()
                ->send();
            return;
        }

        $tutorId = Tutor::where('user_id', Auth::id())->value('id');
        $question->update(['tutor_id' => $tutorId]);

        Notification::make()
            ->title(__('filament-panels.question.question_accepted'))
            ->success()
            ->send();

        return redirect('/tutor/answer-question/' . $question->id);
    }
}
