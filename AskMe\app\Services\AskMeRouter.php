<?php

namespace App\Services;

use App\Enums\UserState;
use App\Models\Student;
use App\Models\Tutor;
use App\Services\SubscriptionService;
use App\Services\QuestionService;
use App\Services\TutorService;
use Illuminate\Support\Facades\Log;

class AskMeRouter
{
    protected  $subscriptionService;
    protected  $questionService;
    protected  $tutorService;



    public function __construct(
        SubscriptionService $subscriptionService,
        QuestionService $questionService,
        TutorService $tutorService,



    ) {
        $this->subscriptionService = $subscriptionService;
        $this->questionService = $questionService;
        $this->tutorService = $tutorService;
    }

    public function process(array $value)
    {
        //  Check if the payload contains a message
        if (!isset($value['messages']) || empty($value['messages'])) {

            return;
        }
        //  Extract the values from the payload
        $message = $value['messages'][0];
        $from = $message['from'];
        $type = $message['type'] ?? null;
        $name = $value['contacts'][0]['profile']['name'] ?? 'عميل واتساب';
        $interactive = $message['interactive'] ?? null;
        $selection  = $interactive['list_reply']['id'] ?? $interactive['button_reply']['id'] ?? null;
        $student = Student::where('number', $from)->first();
        $tutor = Tutor::where('phone', $from)->first();
        if ($tutor) {
            Log::info('tutor', [$tutor]);
        }

        if (!$interactive) {
            return match (true) {
                !empty($tutor)&& $tutor->status === UserState::Init->value => $this->tutorService->defaultMessage($from),
                !empty($tutor)&& $tutor->status === UserState::AwaitingAnswer->value => $this->tutorService->saveTutorAnswer($from, $message),

                empty($student) && empty($tutor) => $this->subscriptionService->sendWelcomeMessage($from, $name),
                !empty($student) && $student->status === UserState::Init->value => $this->subscriptionService->sendWelcomeMessage($from, $name),
                !empty($student) && $student->status === UserState::AwaitingQuestion->value => $this->questionService->handleQuestionSubmission($message),
                !empty($student) && $student->status === UserState::AwaitingComment->value => $this->questionService->saveComment($from,$message),

                default => null
            };
        }
        //  Route interactive selection to the appropriate handler
        return match (true) {
            $selection === 'subscription_info' => $this->subscriptionService->sendSubscriptionInfo($from,),
            $selection === 'subscription_history' => $this->subscriptionService->sendSubscriptionHistory($from),
            $selection === 'subscribe_now' => $this->subscriptionService->subscribeNow($from),
            $selection === 'ask_question' => $this->questionService->sendGradeMessage($from),
            $selection === 'view_questions' => $this->questionService->sendQuestionList($from),
            $selection === 'view_grade_subject' => $this->questionService->viewAvailableGradeAndSubject($from),

            
            str_starts_with($selection, 'grade_') => $this->questionService->sendSubjectMessage($from, str_replace('grade_', '', $selection)),
            str_starts_with($selection, 'subject_') => $this->questionService->sendQuestionMessage($from,  $selection),
            str_starts_with($selection, 'accept_') => $this->questionService->acceptQuestion($from, str_replace('accept_', '', $selection)),

            $selection === 'tutor_questions_list' => $this->tutorService->sendQuestionList($from),
            str_starts_with($selection, 'tutor_view_question_') => $this->tutorService->questionDetails($from, str_replace('tutor_view_question_', '', $selection)),
            str_starts_with($selection, 'answer_') => $this->tutorService->answerQuestion($from, str_replace('answer_', '', $selection)),
            str_starts_with($selection, 'confirm_answer_') => $this->tutorService->confirmAndSendAnswer($from, str_replace('confirm_answer_', '', $selection)),
            str_starts_with($selection, 'cancel_answer_') => $this->tutorService->cancelAnswer($from),

            str_starts_with($selection, 'add_comment_') => $this->questionService->addComment($from,str_replace('add_comment_', '', $selection)),
            str_starts_with($selection, 'student_view_question_') => $this->questionService->questionDetails($from,str_replace('student_view_question_', '', $selection)),

            default => null
        };
    }
}
