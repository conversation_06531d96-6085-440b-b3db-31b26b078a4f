<?php

namespace App\Filament\Admin\Resources\TutorResource\Pages;

use App\Filament\Admin\Resources\TutorResource;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Hash;

class CreateTutor extends CreateRecord
{
    protected static string $resource = TutorResource::class;

    /**
     * This method allows to modify the form data
     * before it's used to create a new Tutor record.
     */
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Extract user fields
        $userData = [
            'name' => $data['user']['name'],
            'email' => $data['user']['email'],
            'password' => Hash::make($this->form->getRawState()['user']['password']),
        ];
        // Create a new User 
        $user = User::create($userData);

        // Assign the new user's ID to the <PERSON><PERSON>'s foreign key
        $data['user_id'] = $user->id;

        // Remove the nested 'user' array 
        unset($data['user']);

        return $data;
    }
}
