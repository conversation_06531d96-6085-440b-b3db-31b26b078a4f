<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CategoriesResource\Pages;
use App\Filament\Resources\ProductsResource\RelationManagers\ProductsRelationManager;
use App\Models\Categories;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class CategoriesResource extends Resource
{
    // Defines the Eloquent model that this Filament resource manages.
    protected static ?string $model = Categories::class;

    // Sets the icon that will be displayed next to the resource in the Filament sidebar navigation.
    protected static ?string $navigationIcon = 'heroicon-m-archive-box';

    /**
     * Defines the form schema for creating and editing Categories records.
     *
     * @param Form $form
     * @return Form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label(__('filament-panels.categories.fields.name')) // Label for the field, translated via <PERSON><PERSON>'s translation helper
                    ->required() // Makes the field mandatory
                    ->maxLength(255), // Sets the maximum length for the input
            ]);
    }

    /**
     * Defines the table schema for listing Categories records.
     *
     * @param Table $table
     * @return Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // A text column to display the 'id' of the category.
                TextColumn::make('id')->label('#')->sortable(), 

                // A text column to display the 'name' of the category.
                TextColumn::make('name')
                    ->label(__('filament-panels.categories.fields.name')) 
                    ->searchable(), // Enables searching by category name

                // A text column to display the 'created_at' timestamp.
                TextColumn::make('created_at')
                    ->label(__('filament-panels.categories.fields.created_at'))
                    ->dateTime(), // Formats the timestamp as a date and time

                // A text column to display the 'updated_at' timestamp.
                TextColumn::make('updated_at')
                    ->label(__('filament-panels.categories.fields.updated_at')) 
                    ->dateTime(), // Formats the timestamp as a date and time
            ])
            ->actions([
                // Action to edit an individual record.
                Tables\Actions\EditAction::make(),
                // Action to delete an individual record.
                Tables\Actions\DeleteAction::make(),
            ])
            ->filters([
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * Get the singular label for the model associated with this resource.
     *
     * @return string
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.categories.singular');
    }

    /**
     * Get the plural label for the model associated with this resource.
     *
     * @return string
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.categories.plural');
    }

    /**
     * Get the navigation label for this resource in the Filament sidebar.
     *
     * @return string
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.categories.title');
    }

    /**
     * Defines the relationships that can be managed directly from this resource.
     *
     * @return array
     */
    public static function getRelations(): array
    {
        return [
            ProductsRelationManager::class,
        ];
    }

    /**
     * Defines the pages associated with this resource.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'), // Page to list all categories
            'create' => Pages\CreateCategories::route('/create'), // Page to create a new category
            'edit' => Pages\EditCategories::route('/{record}/edit'), // Page to edit an existing category
        ];
    }
}
