<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Products extends Model
{
    use HasFactory;
    
    protected $table = 'products';
    protected $fillable = ['name', 'price', 'quantity', 'desc', 'brand', 'website_link', 'product_catelog_id', 'code'];

    public function categories()
    {
        return $this->belongsToMany(Categories::class, 'category_product', 'product_id', 'category_id');
    }

    public function images()
    {
        return $this->hasMany(ProductImages::class, 'product_id');
    }

    public function brand()
    {
        return $this->belongsTo(Brands::class);
    }
    public function orderItems()
    {
        return $this->hasMany(OrderItems::class, 'product_id');
    }


    public function discounts()
    {
        return $this->belongsToMany(Discount::class, 'discount_products', 'product_id', 'discount_id')->withPivot('is_added_catelog');
    }

    public function getActiveDiscount()
    {
        $now = now();

        return $this->discounts()
            ->where('start_at', '<=', $now)
            ->where('end_at', '>=', $now)
            ->latest('start_at')
            ->first();
    }
    public function getExpiredDiscount()
    {
        $now = now();
        return $this->discounts()
            ->where('end_at', '<=', $now)
            ->where('is_added_catalog', true)
            ->latest('start_at')
            ->first();
    }
}
