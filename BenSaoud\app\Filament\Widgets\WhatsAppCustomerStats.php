<?php

namespace App\Filament\Widgets;

use App\Models\Customer; 
use Filament\Widgets\StatsOverviewWidget as BaseWidget; 
use Filament\Widgets\StatsOverviewWidget\Stat; 

/**
 * Class WhatsAppCustomerStats
 *
 * This Filament Stats Overview Widget displays key statistics related to WhatsApp customers.
 * It shows the total number of customers, customers who have placed orders (buyers),
 * and customers who have only chatted (no orders).
 */
class WhatsAppCustomerStats extends BaseWidget
{
    /**
     * @var int|null The sort order for this widget in the Filament dashboard.
     * Lower numbers appear earlier.
     */
    protected static ?int $sort = 4;

    /**
     * Get the heading (title) for the stats overview widget.
     *
     * @return string The translated heading for the widget.
     */
    public function getHeading(): string
    {
        return __('filament-panels.chart.whatsapp_customer_stats');
    }

    /**
     * Defines the statistics to be displayed in the widget.
     *
     * @return array An array of Stat objects, each representing a single statistic.
     */
    protected function getStats(): array
    {
        $totalCustomers = Customer::count();

        $buyers = Customer::whereHas('orders')->count();

        $chatOnly = Customer::whereDoesntHave('orders')->count();

        return [
            Stat::make(
                __('filament-panels.dashboard.data.allCustomerTitle'), 
                $totalCustomers 
            )
                ->description(__('filament-panels.dashboard.data.allCustomer')) 
                ->color('success'), 

            Stat::make(
                __('filament-panels.dashboard.data.buyerCustomerTitle'), 
                $buyers 
            )
                ->description(__('filament-panels.dashboard.data.buyerCustomer')) 
                ->color('warning'), 

            Stat::make(
                __('filament-panels.dashboard.data.chatCustomerTitle'), 
                $chatOnly 
            )
                ->description(__('filament-panels.dashboard.data.chatCustomer')), 
        ];
    }
}