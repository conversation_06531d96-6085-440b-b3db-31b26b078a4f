<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Question;
use Carbon\Carbon;
use Filament\Forms\Components\Select;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Questions<PERSON>hart extends ChartWidget
{

    // Default selected filter range
    public ?string $filter = 'week';

    // Widget sort order on dashboard
    protected static ?int $sort = 7;

    // Full-width widget on dashboard
    protected int | string | array $columnSpan = 'full';

    // Max chart height
    protected static ?string $maxHeight = '300px';
    protected static ?string $heading = 'عدد الاسئلة';


    // Filter options displayed in dropdown
    protected function getFilters(): ?array
    {
        return [
            'week' => __('filament-panels.dashboard.range_week'),
            'month' => __('filament-panels.dashboard.range_month'),
            'year' => __('filament-panels.dashboard.range_year'),
        ];
    }

    // Main method that builds the chart data based on filter
    protected function getData(): array
    {
        $activeFilter = $this->filter;
        Log::info('filter: ' . $activeFilter);

        $labels = [];
        $data = [];

        /**
         * === WEEKLY CHART ===
         * Shows last 7 days (Sun–Sat)
         */
        if ($activeFilter === 'week') {
            $startOfWeek = Carbon::now()->startOfWeek();
            $endOfWeek = Carbon::now()->endOfWeek();

            // Generate 7 days
            $weekDays = collect();
            for ($i = 0; $i <= 6; $i++) {
                $date = $startOfWeek->copy()->addDays($i);
                $weekDays->push([
                    'date' => $date->format('Y-m-d'),
                    'label' => __($date->translatedFormat('l')),
                ]);
            }

            // Group question counts by date
            $questionCounts = Question::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
                ->groupBy('date')
                ->pluck('count', 'date');

            // Combine days with data (fill gaps)
            $weekData = $weekDays->map(function ($day) use ($questionCounts) {
                return [
                    'label' => $day['label'],
                    'count' => $questionCounts[$day['date']] ?? 0,
                ];
            });

            $labels = $weekData->pluck('label')->toArray();
            $data = $weekData->pluck('count')->toArray();
            Log::info("Weekly Data: ", [$data]);

        /**
         * === MONTHLY CHART ===
         * Shows daily question counts from start of month to today
         */
        } elseif ($activeFilter === 'month') {
            $start = now()->startOfMonth();
            $end = now()->startOfDay();

            // Generate daily labels from 1st to today
            $daysInMonth = collect();
            for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
                $daysInMonth->push([
                    'date' => $date->format('Y-m-d'),
                    'label' => __($date->translatedFormat('d M')), 
                ]);
            }

            // daily counts
            $questionCounts = Question::query()
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->whereBetween('created_at', [$start->startOfDay(), $end->endOfDay()])
                ->groupBy('date')
                ->pluck('count', 'date');

            $monthData = $daysInMonth->map(function ($day) use ($questionCounts) {
                return [
                    'label' => $day['label'],
                    'count' => $questionCounts[$day['date']] ?? 0,
                ];
            });

            $labels = $monthData->pluck('label')->toArray();
            $data = $monthData->pluck('count')->toArray();
            Log::info("Monthly Data: ", [$data]);

        /**
         * === YEARLY CHART ===
         * Shows monthly totals from Jan to current month
         */
        } elseif ($activeFilter === 'year') {
            $start = Carbon::now()->startOfYear();
            $end = Carbon::now()->startOfMonth();

            // Build month list from Jan to current
            $months = collect();
            for ($date = $start->copy(); $date->lte($end); $date->addMonth()) {
                $months->push([
                    'month' => $date->format('Y-m'),
                    'label' => __($date->translatedFormat('F')), // January, February...
                ]);
            }

            // Get monthly counts 
            $questionCounts = Question::query()
                ->selectRaw("DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count")
                ->whereBetween('created_at', [$start->startOfDay(), $end->endOfDay()])
                ->groupBy('month')
                ->pluck('count', 'month');

            $yearData = $months->map(function ($month) use ($questionCounts) {
                return [
                    'label' => $month['label'],
                    'count' => $questionCounts[$month['month']] ?? 0,
                ];
            });

            $labels = $yearData->pluck('label')->toArray();
            $data = $yearData->pluck('count')->toArray();
            Log::info("Yearly Data: ", [$data]);
        }

        return [
            'datasets' => [
                [
                    'label' => __('filament-panels.dashboard.questions_label'),
                    'data' => $data,
                    'backgroundColor' => '#3b82f6',
                    'borderColor' => '#2563eb',
                ],
            ],
            'labels' => $labels,
        ];
    }
    protected function getType(): string
    {
        return 'bar';
    }
}
