<?php

use App\Http\Controllers\Dashboard\InvoiceController;
use App\Http\Controllers\Dashboard\LanguageController;
use App\Http\Controllers\Dashboard\MetaCatalogTemplateController;
use App\Http\Controllers\ReportController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});
//Dashboard
Route::get('/admin/orders/{record}/invoice', [InvoiceController::class, 'show'])
    ->name('filament.admin.resources.orders.invoice');

Route::get('/switch-lang/{locale}', [LanguageController::class, 'switchLang'])->name('switch-lang');
Route::get('/whatsapp-template/{format?}', [MetaCatalogTemplateController::class, 'download'])   ->where('format', 'xlsx|csv')
->name('meta.catalog.template');

//For Locale
Route::get('/switch-lang/{locale}', [LanguageController::class, 'switchLang'])->name('switch-lang');

// Reports Routes
Route::prefix('reports')->name('reports.')->group(function () {
    // Sales and Revenue Report
    Route::get('/sales-revenue-pdf', [ReportController::class, 'generateSalesRevenuePdf'])
        ->name('sales_revenue_pdf');
    
    // Inventory Report
    Route::get('/inventory-pdf', [ReportController::class, 'generateInventoryPdf'])
        ->name('inventory_pdf');
});

