<x-filament::page>
    <div class="grid gap-4 max-w-xl">
        <h1 class="text-2xl font-bold">توليد تقرير </h1>

        {{-- Report Type --}}
        <div>

            <label class="block mb-1 text-sm">نوع التقرير</label>
            <select wire:model.defer="reportType" class="w-full border rounded p-2">
                <option value="tutors">تقرير المعلمين</option>
                <option value="revenue">تقرير الإيرادات</option>
                <option value="subscriptions">تقرير الاشتراكات</option>
            </select>
        </div>

        {{-- Range Dropdown --}}
        <div>
            <label class="block mb-1 text-sm">الفترة الزمنية</label>
            <select wire:change="updateRange($event.target.value)" class="w-full border rounded p-2">
                <option value="week">الأسبوع</option>
                <option value="month">الشهر</option>
                <option value="year">السنة</option>
            </select>
        </div>


        {{-- Months Dropdown IF Range is "month" --}}
        @if ($range === 'month')
        <div>
            <label class="block mb-1 text-sm">اختر الشهر</label>
            <select wire:model.defer="selectedMonth" class="w-full border rounded p-2">
                @foreach ($availableMonths as $number => $month)
                <option value="{{ $number }}">{{ $month }}</option>
                @endforeach
            </select>
        </div>
        @endif

        {{-- Generate Button --}}
        <div>
            <x-filament::button wire:click="generateReport" color="primary">
                توليد التقرير
            </x-filament::button>
        </div>
    </div>

    {{-- PDF Preview --}}
    @if ($pdfUrl)
    <div class="mt-10">
        <h2 class="text-lg font-semibold mb-2">معاينة التقرير</h2>
        <iframe src="{{ $pdfUrl }}" width="100%" height="600" class="rounded border shadow-md"></iframe>

        <div class="mt-4">
            <a href="{{ $pdfUrl }}" target="_blank"
                class="inline-flex items-center px-4 py-2 bg-danger-600 text-white rounded hover:bg-danger-700">
                تحميل PDF
            </a>
        </div>
    </div>
    @endif
</x-filament::page>