<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserProductSessions extends Model
{
    use HasFactory;
    
    protected $table = 'user_product_sessions';
    protected $fillable = ['customer_id', 'session_type', 'reference_id', 'last_product_id'];

    public function customer() { return $this->belongsTo(Customer::class); }

}