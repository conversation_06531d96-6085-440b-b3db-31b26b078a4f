<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('whats_app_messages', function (Blueprint $table) {
            $table->id();
            $table->text('message');
            $table->enum('type',['Welcome','WelcomeSubscription','WelcomeTutor','WelcomeTrial']);
            $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
