<?php
namespace App\Services;

use App\Jobs\SendWhatsAppPayment;
use Illuminate\Support\Facades\Log;

class WhatsAppService
{
    public function sendText($to, $message, $provider = 'WhatsAppStore')
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => [
                'body' => $message
            ]
        ];

        SendWhatsAppPayment::dispatch($payload, $provider);
    }

    public function sendList($to, $bodyText, $buttonText, $sections, $provider = 'WhatsAppStore')
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'list',
                'body' => ['text' => $bodyText],
                'action' => [
                    'button' => $buttonText,
                    'sections' => $sections
                ]
            ]
        ];

        SendWhatsAppPayment::dispatch($payload, $provider);
    }

    public function sendButtons($to, $bodyText, $buttons, $provider = 'WhatsAppStore')
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button',
                'body' => ['text' => $bodyText],
                'action' => ['buttons' => $buttons]
            ]
        ];

        SendWhatsAppPayment::dispatch($payload, $provider);
    }
    public function sendTemplate($to, $templateName, $languageCode = 'ar', $components , $provider = 'WhatsAppStore')
{
    $payload = [
        'messaging_product' => 'whatsapp',
        'to' => $to,
        'type' => 'template',
        'template' => [
            'name' => $templateName,
            'language' => [
                'code' => $languageCode
            ],
            'components' => $components
        ]
    ];
    Log::info("payload: ",[$payload]);

    SendWhatsAppPayment::dispatch($payload, $provider);
}

}
