<?php

namespace App\Filament\Widgets;

use App\Models\Orders;
use Filament\Widgets\ChartWidget; 
use Illuminate\Support\Collection;
use App\Services\ReportGenerator;
use Filament\Actions\Action;

/**
 * Class PaymentMethodsPie
 *
 * This Filament Chart Widget displays a doughnut chart showing the distribution
 * of orders by their payment method.
 */
class PaymentMethodsPie extends ChartWidget
{
    /**
     * @var int|null The sort order for this widget in the Filament dashboard.
     * Lower numbers appear earlier.
     */
    protected static ?int $sort = 3;

    /**
     * @var string|null The maximum height of the chart container.
     * This helps control the widget's layout on the dashboard.
     */
    protected static ?string $maxHeight = '300px';

    /**
     * Get the heading (title) for the chart widget.
     *
     * @return string The translated heading for the chart.
     */
    public function getHeading(): string
    {
        // Returns a translated string for the chart's title,
        // typically defined in Filament's language files.
        return __('filament-panels.chart.payment_methods');
    }

    /**
     * Prepares the data for the chart.
     * This method fetches order counts by payment method and formats them for Chart.js.
     *
     * @return array An associative array containing 'labels' and 'datasets' for the chart.
     */
    protected function getData(): array
    {
        // Define a consistent color palette for your chart.
        $colorPalette = [
            'rgb(245, 208, 23)', 
            'rgb(59, 144, 255)',  
            '#f6ad55',            // A warm orange (HEX)
            '#ed8936',            // A deeper orange (HEX)
            '#ecc94b',            // A soft yellow (HEX)
            '#9f7aea',            // A rich purple (HEX)
            '#e53e3e',            // A strong red (HEX)
            '#a0aec0',            // A neutral grey (HEX)
        ];

        // Query the Orders table to get the count of orders for each payment method.
        $data = Orders::selectRaw("
            COALESCE(payment_method, 'Unspecified') as method,
            COUNT(*) as count")
            ->groupBy('method') // Group the results by payment method
            ->pluck('count', 'method'); // Convert the result into a key-value pair collection
                                        // where method is the key and total count is the value.

        // Map payment methods to colors from the defined palette.
        $backgroundColors = $this->assignColorsToMethods($data->keys(), $colorPalette);

        return [
            'datasets' => [ 
                [
                    'label' => 'Orders by Payment Method', // Label for the dataset, shown in legend/tooltip
                    'data' => $data->values()->toArray(), // Array of numerical values (counts)
                    'backgroundColor' => $backgroundColors->toArray(), // Array of colors corresponding to each data point
                    'hoverOffset' => 4, // Adds a slight pop-out effect when hovering over a slice
                ],
            ],
            'labels' => $data->keys()->map(function ($method) {
                // Format the labels by capitalizing the first letter of each method name.
                return ucfirst($method);
            })->toArray(), 
        ];
    }

    /**
     * Defines the options and configuration for the chart.
     * This includes responsiveness, legend position, tooltip styling, and doughnut specifics.
     *
     * @return array An associative array of Chart.js options.
     */
    protected function getOptions(): array
    {
        return [
            'responsive' => true, // Allows the chart to resize with its container
            'maintainAspectRatio' => false,
            'plugins' => [ 
                'legend' => [
                    'position' => 'bottom', // Position the legend at the bottom of the chart
                    'labels' => [ // Styling for the legend labels
                        'color' => '#4b5563', // Darker text color for better contrast
                        'padding' => 20,      // Padding between legend items
                        'font' => [           // Font styling for legend labels
                            'size' => 14,     // Font size
                            'weight' => 'bold', // Font weight
                        ],
                    ],
                ],
                'tooltip' => [ 
                    'enabled' => true,     
                    'backgroundColor' => '#333', // Background color of the tooltip
                    'titleColor' => '#fff',      // Color of the tooltip title
                    'bodyColor' => '#fff',       // Color of the tooltip body text
                    'borderColor' => '#555',     // Border color of the tooltip
                    'borderWidth' => 1,          // Border width of the tooltip
                ],
            ],
            'cutout' => '70%', // Defines the size of the inner hole for a doughnut chart (70% of the radius)
            'layout' => [ // Layout options for the chart
                'padding' => 0, // No padding around the chart itself
            ],
        ];
    }

    /**
     * Defines the type of chart to be rendered.
     *
     * @return string The chart type
     */
    protected function getType(): string
    {
        return 'doughnut';
    }

    /**
     * Assigns colors to payment methods based on a predefined palette.
     * It cycles through the palette if there are more methods than colors.
     *
     * @param Collection $methods A collection of payment method names (keys from the data).
     * @param array $palette An array of color codes to be used.
     * @return Collection A collection of assigned color values, in the order of the methods.
     */
    private function assignColorsToMethods(Collection $methods, array $palette): Collection
    {
        $assignedColors = new Collection(); // Initialize a new collection to store assigned colors
        $paletteIndex = 0; // Initialize an index to cycle through the color palette

        foreach ($methods as $method) {
            // Assign a color from the palette to the current method.
            // The modulo operator (%) ensures that the palette index wraps around
            $assignedColors->put($method, $palette[$paletteIndex % count($palette)]);
            $paletteIndex++; // Increment the palette index for the next method
        }

        // Return only the color values from the assignedColors collection.
        return $assignedColors->values();
    }

    /**
     * Get the actions available for the widget.
     *
     * @return array
     */
    protected function getHeaderActions(): array
    {
        return [
            Action::make('download_report')
                ->label(__('Download Analysis Report'))
                ->icon('heroicon-o-document-arrow-down')
                ->action(function () {
                    $generator = new ReportGenerator();
                    $path = $generator->generateAnalysisReport();
                    
                    return response()->download($path)->deleteFileAfterSend();
                }),
        ];
    }
}