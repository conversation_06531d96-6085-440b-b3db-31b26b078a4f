<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id(); 
            $table->decimal('amount', 8, 2);
            $table->string('currency');
            $table->enum('status', ['PENDING', 'COMPLETED', 'FAILED']);
            $table->enum('payment_provider', ["TLYNC","STRIPE"]);
            $table->string('uuid');
            $table->string('externalRef');
            $table->foreignId('subscription_packages_id')->constrained();
            $table->foreignId('student_id')->constrained();
            $table->timestamps();
        });
    }

    public function down(): void {
        Schema::dropIfExists('transactions');
    }
};
