<?php

namespace App\Jobs;

use App\Enums\Currency;
use App\Enums\PaymentProvider;
use App\Enums\TransactionStatus;
use App\Services\WhatsAppService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use App\Models\AskMe\Student;
use App\Models\AskMe\SubscriptionPackage;
use App\Models\AskMe\Transaction;
use App\Models\Products;

class WhatsAppStoreTlync implements ShouldQueue
{
    use Queueable;
    public $tries = 1;
    public $maxExceptions = 1;
    public $timeout = 60;

    protected string $phone;
    protected int $productId;

    public function __construct(string $phone, int $productId)
    {
        $this->phone = $phone;
        $this->productId = $productId;
    }
    /**
     * Initiates a payment request to T-lync payment gateway.
     * 
     * 1- Validates the incoming request.
     * 2- Extracts the amount, phone number, and subscription plan ID.
     * 3- Generates a unique reference for the transaction.
     * 4- Validates the uniqueness of that reference.
     * 5- Prepares the API payload.
     * 6- Calls the T-lync payment gateway API.
     * 7- Handles the API response.
     * 8- Updates the subscription status and creates a new transaction record.
     * 10- Dispatches a job to check the transaction status.
     */

    public function handle()
    {
        try {
            $customRef = Str::uuid()->toString();

            $validator = Validator::make(
                [
                    'custom_ref' => $customRef,
                    'phone' => $this->phone,
                    'product_id' => $this->productId,
                ],
                [
                    'custom_ref' => 'required|string|unique:transactions,uuid',
                    'phone' => 'required|regex:/^\\+2189\\d{8}$/',
                    'product_id' => 'required|integer',
                ],
            );

            if ($validator->fails()) {
                throw new ValidationException($validator);
            }

            $amount = Products::where('id', $this->productId)->value('price');
            if (!$amount) {
                throw new \Exception("Plan not found");
            }

            $number=ltrim($this->phone, '+');
            $student = Student::where('number', $number)->first();
            Log::info("Student: " . $student);
            if (!$student) {
                throw new \Exception("Student not found");
            }

            $data = [
                'id' => "n9NJlYb69Ln7mrXo0djPEKVa34Qe6AvGXdZkxN5wyOBzRD1bMlJY2WGgqX5gQBro" ,
                'amount' => $amount,
                'phone' => $this->phone,
                'backend_url' => config('api.askme_backend_url'),
                'custom_ref' => $customRef,
                'frontend_url' => config('api.askme_frontend_url') . '/' . $customRef,
                'failed_front_end_url' => config('api.askme_failed_frontend_url') . '/' . $customRef,
            ];

            Log::info(' AskMe Tlync Payment Payload', [$data]);

            $response = Http::timeout(60)
                ->withHeaders(['Accept' => 'application/json'])
                ->withToken('JuAFXvEeYLhGqOGJRa8vRhSGYnForLot1Rmit3tT')
                ->post('https://uat-api.tlync.ly/api/v1/intiate/apipayment/', $data);
            if (!$response->successful()) {
                Log::error('Tlync API returned error: ' . $response->body());
                $this->notifyPaymentFailure();
                return;
            }
            $responseData = $response->json();

            DB::transaction(function () use ($data, $student) {
                $transaction = Transaction::create([
                    'email' => '',
                    'phone' => $data['phone'],
                    'subscription_packages_id' => $this->planId,
                    'payment_provider' => PaymentProvider::TLYNC,
                    'amount' => $data['amount'],
                    'currency' => Currency::LYD,
                    'status' => TransactionStatus::PENDING,
                    'uuid' => $data['custom_ref'],
                    'externalRef' => '',
                    "student_id" => $student->id
                ]);

            });

            // Send WhatsApp message with payment URL
            $plan = SubscriptionPackage::find($this->planId);
            $price = number_format($plan->price, 2);
            $currency = 'دينار ليبي';
            $paymentUrl = $responseData['url'];
            $message = " *لقد اخترت الباقة  {$plan->question_limit} عدد الاسئلة {$plan->period} بسعر {$price} {$currency}*\n";
            $message .= "اضغط على الرابط أدناه لاكمال عملية الدفع:\n{$paymentUrl}";

            try {
                (new WhatsAppService())->sendText($this->phone, $message);
            } catch (\Exception $e) {
                Log::error('Failed to send WhatsApp message: ' . $e->getMessage());
            }
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
            $this->notifyPaymentFailure();
        }
    }

    private function notifyPaymentFailure(): void
    {
        try {
            $msg =  "لايمكن اتمام عملية الدفع,  الرجاء المحاولة لاحقاً.";
            (new WhatsAppService())->sendText($this->phone, $msg,);
        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp failure message: ' . $e->getMessage());
        }
    }
}
