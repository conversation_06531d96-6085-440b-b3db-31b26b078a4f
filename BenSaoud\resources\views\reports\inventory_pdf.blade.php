<!DOCTYPE html>
<html dir="rtl" lang="ar">
    
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>تقرير حالة المخزون</title>
    <style>
        
        body { 
            font-family: xbriyaz;
            margin: 20px; 
            font-size: 14px; 
            color: #333;
            direction: rtl;
            line-height: 1.6;
            text-align: right;
        }
        h1 { 
            font-family: xbriyaz;
            color: #2563eb; 
            text-align: center; 
            margin-bottom: 20px; 
            font-size: 26px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        h2 { 
            font-family: xbriyaz;
            color: #1e40af; 
            margin-top: 25px; 
            margin-bottom: 15px; 
            border-bottom: 1px solid #e0e0e0; 
            padding-bottom: 8px; 
            font-size: 20px; 
        }
        .summary { 
            background-color: #f8fafc; 
            padding: 15px; 
            border-radius: 8px; 
            margin-bottom: 25px;
            border: 1px solid #e2e8f0;
        }
        .summary p { 
            margin: 0; 
            line-height: 1.6; 
            color: #334155; 
        }
        .metrics-container {
            display: block;
            margin-bottom: 25px;
        }
        .metric-card {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .metric-card p { margin: 0; }
        .metric-name { 
            font-size: 15px; 
            color: #475569; 
            font-weight: bold;
        }
        .metric-value { 
            font-size: 24px; 
            color: #1e293b; 
            font-weight: bold; 
            margin: 8px 0; 
        }
        .metric-change { 
            font-size: 14px; 
            display: block;
            margin-top: 5px;
        }
        .trend-up { color: #059669; }
        .trend-down { color: #dc2626; }
        .trend-neutral { color: #6b7280; }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-bottom: 25px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        th, td { 
            border: 1px solid #e2e8f0; 
            padding: 10px; 
            text-align: right; 
        }
        th { 
            background-color: #f1f5f9; 
            color: #334155; 
            font-weight: bold; 
            font-size: 14px; 
        }
        tr:nth-child(even) { background-color: #f8fafc; }
        .footer { 
            text-align: center; 
            margin-top: 40px; 
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            font-size: 12px; 
            color: #64748b; 
        }
        .currency { 
            font-family: xbriyaz;
            direction: ltr;
            display: inline-block;
        }
        .status-out { color: #dc2626; }
        .status-low { color: #eab308; }
        .status-ok { color: #059669; }
    </style>
</head>
<body>
    <h1>{!! $reportData['title'] !!}</h1>
    <div class="summary">
        <p><strong>تاريخ التقرير:</strong> {!! $reportData['date'] !!}</p>
        <p>{!! $reportData['summary'] !!}</p>
    </div>

    <h2>مؤشرات المخزون الرئيسية</h2>
    <div class="metrics-container">
        @foreach($reportData['metrics'] as $metric)
            <div class="metric-card">
                <p class="metric-name">{!! $metric['name'] !!}</p>
                <p class="metric-value">{!! $metric['value'] !!}</p>
                <p class="metric-change {{ $metric['trend'] === 'up' ? 'trend-up' : ($metric['trend'] === 'down' ? 'trend-down' : 'trend-neutral') }}">
                    {!! $metric['change'] !!}
                    @if($metric['trend'] === 'up')
                        &#9650;
                    @elseif($metric['trend'] === 'down')
                        &#9660;
                    @else
                        &#8212;
                    @endif
                </p>
            </div>
        @endforeach
    </div>

    <h2>تحليل المخزون حسب الفئة</h2>
    <table>
        <thead>
            <tr>
                <th>الفئة</th>
                <th>إجمالي الوحدات</th>
                <th>القيمة الإجمالية</th>
                <th>المنتجات منخفضة المخزون</th>
            </tr>
        </thead>
        <tbody>
            @foreach($reportData['categoryAnalysis'] as $category)
                <tr>
                    <td>{!! $category['category'] !!}</td>
                    <td>{{ number_format($category['total_items']) }}</td>
                    <td class="currency">${{ number_format($category['total_value'], 2) }}</td>
                    <td>{{ number_format($category['low_stock_items']) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <h2>تفاصيل المخزون</h2>
    <table>
        <thead>
            <tr>
                <th>المنتج</th>
                <th>الكود</th>
                <th>العلامة التجارية</th>
                <th>الفئة</th>
                <th>المخزون</th>
                <th>السعر</th>
                <th>القيمة</th>
                <th>المبيعات</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            @foreach($reportData['products'] as $product)
                <tr>
                    <td>{!! $product['name'] !!}</td>
                    <td>{!! $product['code'] !!}</td>
                    <td>{!! $product['brand'] !!}</td>
                    <td>{!! $product['category'] !!}</td>
                    <td>{{ number_format($product['stock']) }}</td>
                    <td class="currency">${{ number_format($product['price'], 2) }}</td>
                    <td class="currency">${{ number_format($product['value'], 2) }}</td>
                    <td>{{ number_format($product['total_sold']) }}</td>
                    <td class="{{ $product['status'] === 'نفذ من المخزون' ? 'status-out' : ($product['status'] === 'مخزون منخفض' ? 'status-low' : 'status-ok') }}">
                        {!! $product['status'] !!}
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>تم إنشاء التقرير في {!! date('Y-m-d H:i:s') !!}</p>
        <p>© {!! date('Y') !!} BenSaoud. جميع الحقوق محفوظة.</p>
    </div>
</body>
</html> 