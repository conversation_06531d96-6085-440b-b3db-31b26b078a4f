<?php

namespace App\Providers\Filament;

use App\Filament\Admin\Pages\Reports;
use App\Filament\Admin\Pages\Settings;
use App\Filament\Admin\Widgets\AdminCustomStats;
use App\Filament\Admin\Widgets\AdminFinanceStats;
use App\Filament\Admin\Widgets\AdminGeneralStats;
use App\Filament\Admin\Widgets\PackagePerformanceTable;
use App\Filament\Admin\Widgets\QuestionsChart;
use App\Filament\Admin\Widgets\RevenueChart;
use App\Filament\Admin\Widgets\RevenueComparisonChart;
use App\Filament\Admin\Widgets\SubscriptionPackagePieChart;
use App\Filament\Admin\Widgets\TopTutors;
use App\Filament\Admin\Widgets\TransactionStatusPieChart;
use App\Http\Middleware\SetLocale;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Outerweb\FilamentSettings\Filament\Plugins\FilamentSettingsPlugin;

class AdminPanelPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('admin')
            ->path('adminPanel')
            ->brandName('ادرس لتعليم الالكتروني')
            ->login()
            ->spa()
            ->unsavedChangesAlerts()
            ->colors([
                'primary' => Color::Blue,
            ])->userMenuItems([
                MenuItem::make()
                    ->label(fn(): string => app()->getLocale() === 'ar' ? 'English' : 'العربية')
                    ->url(fn(): string => '/switch-lang/' . (app()->getLocale() === 'ar' ? 'en' : 'ar'))
                    ->icon('heroicon-m-language'),
            ])
            ->plugins([
                FilamentSettingsPlugin::make()
                    ->pages([
                        Settings::class,
                    ])
            ])
            ->discoverResources(in: app_path('Filament/Admin/Resources'), for: 'App\\Filament\\Admin\\Resources')
            ->discoverPages(in: app_path('Filament/Admin/Pages'), for: 'App\\Filament\\Admin\\Pages')
            ->pages([
                Pages\Dashboard::class,
                Reports::class
            ])
            ->discoverWidgets(in: app_path('Filament/Admin/Widgets'), for: 'App\\Filament\\Admin\\Widgets')
            ->widgets([
                AdminGeneralStats::class,
                QuestionsChart::class,
                TopTutors::class,
                AdminFinanceStats::class,
                RevenueComparisonChart::class,
                SubscriptionPackagePieChart::class,
                TransactionStatusPieChart::class,
                RevenueChart::class,
                PackagePerformanceTable::class
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SetLocale::class,

            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
