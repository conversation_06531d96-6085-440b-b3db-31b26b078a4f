<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('whatsappTransactions', function (Blueprint $table) {
                 $table->dropColumn('status');
                 $table->dropColumn('payment_provider');
                 // Create new enum column
                 $table->enum('status', ["PENDING","COMPLETED","FAILED","CANCELED"])->default('PENDING');
                 $table->enum('payment_provider', ["TLYNC","STRIPE"]);
     
              
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trans', function (Blueprint $table) {
            //
        });
    }
};
