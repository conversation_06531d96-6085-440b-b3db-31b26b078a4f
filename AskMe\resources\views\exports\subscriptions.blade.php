<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            direction: rtl;
            text-align: right;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #eee;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .divider {
            border: none;
            border-top: 2px solid #333;
            margin: 20px 0;
        }
    </style>
</head>

<body>

    <div class="header">
        <h1>أدرس للتعليم الإلكتروني</h1>
        <h3>خدمة اسألني</h3>

        <p style="font-size: 18px; font-weight: bold; margin-top: 10px;">
            تقرير الاشتراكات
        </p>

        {{-- الفترة الزمنية --}}
        <p style="margin-top: 5px;">
            @if ($range == 'month' && $month)
                لشهر {{ $month }}
            @elseif ($range == 'year')
                للسنة الحالية: {{ now()->format('Y') }}
            @elseif ($range == 'week')
                للأسبوع من {{ $weekStart }} إلى {{ $weekEnd }}
            @else
                للفترة المحددة
            @endif
        </p>

        <p style="margin-top: 10px; font-size: 14px; color: #666;">
            هذا التقرير يعرض عدد الاشتراكات اليومية بالإضافة إلى توزيع الاشتراكات حسب الباقات خلال الفترة الزمنية المختارة.
        </p>
    </div>

    <hr class="divider">

    {{-- ملخص الاشتراكات --}}
    <div>
        <p><strong>عدد الاشتراكات الكلي:</strong> {{ $totalSubscriptions }}</p>
    </div>

    <hr class="divider">

    {{-- تفاصيل الاشتراكات اليومية --}}
    <div>
        <h3 style="margin-top: 20px;">تفاصيل الاشتراكات اليومية:</h3>
        <table>
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>عدد الاشتراكات</th>
                </tr>
            </thead>
            <tbody>
                @forelse($dailySubscriptions as $day)
                    <tr>
                        <td>{{ \Carbon\Carbon::parse($day->date)->format('d/m/Y') }}</td>
                        <td>{{ $day->subscriptions_count }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="2" style="text-align: center; padding: 10px;">لا توجد بيانات متاحة.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <hr class="divider">

    {{-- تفاصيل الاشتراكات حسب الباقات --}}
    <div>
        <h3 style="margin-top: 20px;">تفاصيل الاشتراكات حسب الباقات:</h3>
        <table>
            <thead>
                <tr>
                    <th>اسم الباقة</th>
                    <th>عدد الاشتراكات</th>
                </tr>
            </thead>
            <tbody>
                @forelse($packageSubscriptions as $pkg)
                    <tr>
                        <td>{{ $pkg->package->name ?? 'باقة غير معروفة' }}</td>
                        <td>{{ $pkg->subscriptions_count }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="2" style="text-align: center; padding: 10px;">لا توجد بيانات متاحة.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

</body>
