<?php

namespace App\Filament\Admin\Resources;

use App\Enums\TutorActivities;
use App\Filament\Admin\Resources\TutorActivityResource\Pages;
use App\Filament\Admin\Resources\TutorActivityResource\RelationManagers;
use App\Models\TutorActivity;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TutorActivityResource extends Resource
{
    protected static ?string $model = TutorActivity::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    public function getTitle(): string
    {
        return __('filament-panels.tutor_activities.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.tutor_activities.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.tutor_activities.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.tutor_activities.title');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            TextColumn::make('tutor.user.name')->label(__('filament-panels.tutor.fields.name'))->searchable(),

            TextColumn::make('question.student.name')->label(__('filament-panels.question.fields.name'))->searchable(),

            TextColumn::make('question.student.number')->label(__('filament-panels.tutor.fields.phone'))->searchable(),

            TextColumn::make('question.id')->label(__('filament-panels.question.fields.id')),

            TextColumn::make('action')
                ->label(__('filament-panels.tutor_activities.singular'))
                ->badge()
                ->formatStateUsing(fn(string $state) => TutorActivities::from($state)->label())
                ->color(fn(string $state) => TutorActivities::from($state)->color()),

            TextColumn::make('created_at')->dateTime()->label(__('filament-panels.question.fields.created_at')),
        ])  ->filters([
            SelectFilter::make('action')
                ->label(__('filament-panels.tutor_activities.singular'))
                ->options([
                    TutorActivities::Accept->value => TutorActivities::Accept->label(),
                    TutorActivities::Answer->value => TutorActivities::Answer->label(),
                    TutorActivities::Edit->value   => TutorActivities::Edit->label(),
                ])
                ->attribute('action')
                ->default(null),
        ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTutorActivities::route('/'),
        ];
    }
}
