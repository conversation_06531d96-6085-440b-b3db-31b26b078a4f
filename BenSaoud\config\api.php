<?php

return [
    'api_token' => env('API_TOKEN'),
    'store_id' => env('STORE_ID'),
    'frontend_url' => env('FRONTEND_URL'),
    'failed_frontend_url' => env('FAILED_FRONTEND_URL'),
    'backend_url' => env('BACKEND_URL'),
    'payment_api_live' => env('PAYMENT_API_LIVE'),
    'payment_api_test' => env('PAYMENT_API_TEST'),
    'get_transaction_receipt_api_live' => env('GET_TRANSACTION_RECEIPT_API'),

    // iSend Settings
    'isend_url' => env('ISEND_URL'),
    'isend_token' => env('ISEND_TOKEN'),
    'iSend_id' => env('ISEND_ID'),

    //Whatsapp API
    'whatsapp_api_url' => env('WHATSAPP_API_URL'),
    'whatsapp_phone_id' => env('WHATSAPP_PHONE_ID'),
    'whatsapp_store_phone_id' => env('WHATSAPP_STORE_PHONE_ID'),
    'whatsapp_access_token' => env('WHATSAPP_ACCESS_TOKEN'),
    'whatsapp_template_account' => env('WHATSAPP_TEMPLATE_ACCOUNT'),
    'whatsapp_template_support' => env('WHATSAPP_TEMPLATE_SUPPORT'),
    'whatsapp_template_otp' => env('WHATSAPP_TEMPLATE_OTP'),
    'webhook_verify_token' => env('WEBHOOK_VERIFY_TOKEN'),
    'store_order_number' => env('STORE_ORDER_NUMBER'),
    'catelog_id' => env('CATELOG_ID'),

    
    // Tlync for whatsapp pay
    'whatsapp_frontend_url' => env('WHATSAPP_FRONTEND_URL'),
    'whatsapp_failed_frontend_url' => env('WHATSAPP_FAILED_FRONTEND_URL'),
];
