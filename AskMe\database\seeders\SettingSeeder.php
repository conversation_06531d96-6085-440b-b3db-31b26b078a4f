<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'subsription_requier',
                'value' => 0,
            ],
            [
                'key' => 'question_limit',
                'value' => 3,
            ],

        ];

        foreach ($settings as $setting) {
            Setting::set($setting['key'], $setting['value']);
        }
    
    }
}
