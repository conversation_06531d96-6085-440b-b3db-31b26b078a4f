<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use App\Models\Products;
use App\Services\CatalogSyncService;
use Closure;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProductResource extends Resource
{
    protected static ?string $model = Products::class;

    protected static ?string $navigationIcon = 'heroicon-m-archive-box-arrow-down';

    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')
                ->label(__('filament-panels.products.fields.name'))
                ->required(),
            TextInput::make('code')
                ->label(__('filament-panels.products.fields.code'))
                ->required(),

            Textarea::make('desc')
                ->label(__('filament-panels.products.fields.desc'))
                ->required(),

            TextInput::make('price')
                ->label(__('filament-panels.products.fields.price'))
                ->numeric()
                ->minValue(1)
                ->required(),

            TextInput::make('quantity')
                ->label(__('filament-panels.products.fields.quantity'))
                ->numeric()
                ->minValue(1)
                ->required(),

            TextInput::make('website_link')
                ->label(__('filament-panels.products.fields.link'))
                ->rule('url')
                ->required(),

            Select::make('categories')
                ->label(__('filament-panels.products.fields.categories'))
                ->relationship('categories', 'name') // Many-to-Many
                ->multiple()
                ->preload()
                ->searchable(),

            Repeater::make('images')
                ->label(__('filament-panels.products.fields.image'))
                ->schema([
                    FileUpload::make('image_url')
                        ->label(__('filament-panels.products.fields.image'))
                        ->image()
                        ->directory('products')
                        ->disk('public')
                        ->visibility('public')
                        ->imagePreviewHeight(150)
                        ->openable()
                        ->downloadable()
                ])
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable(),
                ImageColumn::make('images.0.image_path')
                    ->label(__('filament-panels.products.fields.image'))->getStateUsing(function ($record) {
                        $path = data_get($record, 'images.0.image_url');
                        if (filter_var($path, FILTER_VALIDATE_URL)) {
                            return $path;
                        }
                        return asset('storage/' . $path);
                    })
                    ,
                TextColumn::make('name')->searchable()
                    ->label(__('filament-panels.products.fields.name')),
                TextColumn::make('code')->searchable()
                    ->label(__('filament-panels.products.fields.code')),

                TextColumn::make('price')->label(__('filament-panels.products.fields.price'))
                    ->formatStateUsing(fn($state) => number_format($state, 2) . ' LYD'),
                TextColumn::make('active_discount')
                    ->label(__('filament-panels.products.fields.discount'))
                    ->getStateUsing(function ($record) {
                        $discount = $record->getActiveDiscount();
                        return $discount?->value ?? 'no_discount';
                    })
                    ->formatStateUsing(function ($state, $record) {
                        if ($state === 'no_discount') {
                            return '—';
                        }
                        return is_numeric($state) ? "{$state}%" : $state;
                    })
                    ->badge()
                    ->alignment('center')
                    ->color(fn($record) => $record->getActiveDiscount() ? 'success' : 'secondary'),

                TextColumn::make('price_after_discount')
                    ->label(__('filament-panels.products.fields.discountPrice'))
                    ->getStateUsing(function ($record) {
                        $discount = $record->getActiveDiscount();
                        $price = $record->price;
                        if (!$discount) {
                            return  '—';
                        }

                        return max($price - ($price * $discount->value / 100), 0);
                    })
                    ->formatStateUsing(fn($state) => $state === '—' ? '—' : number_format($state, 1) . ' LYD')
                    ->alignment('center')
                    ->badge(fn($state) => $state !== '—')
                    ->color(fn($state) => $state !== '—'? 'primary' : 'gray'),

                TextColumn::make('categories.name')
                    ->label(__('filament-panels.products.fields.categories'))
                    ->badge()->separator(', '),

                TextColumn::make('created_at')->dateTime()->label(__('filament-panels.products.fields.created_at')),
                TextColumn::make('updated_at')->dateTime()->label(__('filament-panels.products.fields.updated_at')),


            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->after(function ($record) {
                        Log::info("Product deleted from table: " . $record->id);
                        app(CatalogSyncService::class)->deleteProduct($record->product_catelog_id);
                    }),
            ])
        ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.products.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.products.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.products.title');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
