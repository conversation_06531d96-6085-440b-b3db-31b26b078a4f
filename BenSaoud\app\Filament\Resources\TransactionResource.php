<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransactionResource\Pages;
use App\Filament\Resources\TransactionResource\RelationManagers;
use App\Models\Transaction;
use App\Models\WhatsappTransactions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

/**
 * TransactionResource
 * 
 * This resource manages WhatsApp transactions in the Filament admin panel.
 * It provides a view of transaction details including customer information,
 * payment amounts, status, and timestamps.
 */
class TransactionResource extends Resource
{
    protected static ?string $model = WhatsappTransactions::class;

    protected static ?string $navigationIcon = 'heroicon-s-currency-dollar';

    /**
     * Define the form fields for creating and editing transactions
     * Currently empty as transactions are managed through WhatsApp
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form fields not implemented
            ]);
    }

    /**
     * Define the table columns and actions for listing transactions
     * Shows transaction details including customer info, amount, status, and timestamps
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Customer name from the related customer model
                TextColumn::make('customer.name')->label(__('filament-panels.discount.fields.name')),
                // Transaction amount formatted with 2 decimal places and LYD currency
                TextColumn::make('amount')->label(__('filament-panels.transactions.fields.amount'))->formatStateUsing(fn($state) => number_format($state, 2) . ' LYD'),
                // Transaction status 
                TextColumn::make('status')->label(__('filament-panels.transactions.fields.status')),
                // Payment provider used for the transaction
                TextColumn::make('payment_provider')->label(__('filament-panels.transactions.fields.payment_provider')),
                // Creation timestamp
                TextColumn::make('created_at')->dateTime()->label(__('filament-panels.products.fields.created_at')),
                // Last update timestamp
                TextColumn::make('updated_at')->dateTime()->label(__('filament-panels.products.fields.updated_at')),
            ])
            ->filters([
                // No filters defined
            ])
            ->actions([
            ])
            ->bulkActions([
            
            ]);
    }

    /**
     * Get the singular label for the model
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.transactions.singular');
    }
    
    /**
     * Get the plural label for the model
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.transactions.plural');
    }
    
    /**
     * Get the navigation label for this resource
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.transactions.title');
    }

    /**
     * Define any relationships to be displayed in the resource
     */
    public static function getRelations(): array
    {
        return [
            // No relationships defined
        ];
    }

    /**
     * Define the available pages for this resource
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
          
        ];
    }
}
