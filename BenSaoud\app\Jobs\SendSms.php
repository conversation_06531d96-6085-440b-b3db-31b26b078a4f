<?php

namespace App\Jobs;

use App\Models\Transaction;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/*
    This job is used to send subscription via SMS using iSend API
    This job gets triggered after successful payment
    This job recieves the user data in variable called transaction
*/

class SendSms implements ShouldQueue
{
    use Queueable;

    // Setting the job to try sending 3 times
    public $tries = 3;
    // Every try have gap of 1 minute
    public $backoff = 60; 

    protected Transaction $transaction;

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    //* This function handles the logic of using iSend API to send subscription via SMS *//
    public function handle(): void
    {
        try{
            // Setting up the message
            $message = "إشتراك منصة أدرس\nالبريد الإلكتروني: {$this->transaction->subscription->email}\nرمز المرور: {$this->transaction->subscription->password}";
           
            // Data payload for iSend API
            $data =[
                'recipient' => $this->transaction->phone,
                'message' => $message,
                'sender_id' => config('api.iSend_id'),
                'type' => 'unicode',
            ];

            // Call iSend API with the data payload
            $response = Http::timeout(60)
            ->withHeaders([
                'Authorization' => 'Bearer ' . config('api.isend_token'),
                'Accept' => 'application/json',
            ])
            ->post(config('api.isend_url'), $data);

            if($response->successful()){

                $responseData = $response->json();

                Log::info('response data: ', [$responseData]);

                if ($responseData['status'] === 'success') {

                    Log::info('SMS sent successfully: ', $responseData);
                    
                } else {

                    throw new \Exception('API returned an error: ' . json_encode($responseData));
                }

            }else{

                throw new \Exception('API request failed: ' . json_encode($response->json()));
            }

        // If there is an error the job will retry sending the SMS
        }catch(\Exception $error){
            Log::error('SMS message sending failed: ', ['error' => $error->getMessage()]);
            throw $error;
        }
    }
}
