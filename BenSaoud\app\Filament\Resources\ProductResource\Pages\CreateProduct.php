<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use App\Jobs\AddToCatelog;
use App\Models\Products;
use App\Services\CatalogSyncService;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CreateProduct extends CreateRecord
{
    protected static string $resource = ProductResource::class;

    /**
     * Handles product creation with an sync to Meta Catalog.
     * This runs inside a database transaction, and sends the created product
     * to Facebook catalog API after a successful save.
     *
     * @param array $data The validated form input
     * @return Model The created product model
     * @throws \Exception If the API response contains an error or on database
     */
    protected function handleRecordCreation(array $data): Model
    {
        return DB::transaction(function () use ($data) {
            // Create the product
            $product = Products::create($data);
            if (!$product) {
                throw new \Exception('Failed to add product.');
            }
            
            $images = $data["images"] ?? [];
            unset($data['images']);
            foreach ($images as $image) {
                $product->images()->create([
                    'product_id' => $product->id,
                    'image_url' => $image['image_url'],

                ]);
            }
            // Send to Meta Catalog
            $response = app(CatalogSyncService::class)->createProduct($product);

            // Handle Meta response
            $responseJson = $response->json();

            if (isset($responseJson['error'])) {
                Log::error('Meta Catalog API Error:', $responseJson);
                throw new \Exception($responseJson['error']['message'] ?? 'Failed to add product.');
            }
            //Add Id come for meta catelog to the database
            $product->update(['product_catelog_id' => $responseJson['id']]);

            return $product;
        });
    }
}
