<?php

// app/Filament/Pages/Settings/Settings.php

namespace App\Filament\Admin\Pages;

use App\Enums\WhatsAppMessageType;
use App\Filament\Settings\Forms\AppSettingsTab;
use App\Filament\Settings\Forms\CatalogTemplate;
use App\Models\User;
use App\Models\WhatsAppMessages;
use Closure;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Outerweb\FilamentSettings\Filament\Pages\Settings as BaseSettings;
use Outerweb\Settings\Models\Setting;

class Settings extends BaseSettings
{
    protected static ?int $navigationSort = 3;

    public function mount(): void
    {
        parent::mount();

        // Load messages from the database
        $this->form->fill([
            'subscription_required' => Setting::get('subscription_required'),
            'question_limit' => Setting::get('question_limit'),
            WhatsAppMessageType::WELCOME->value => WhatsAppMessages::where('type', WhatsAppMessageType::WELCOME)->value('message'),
            WhatsAppMessageType::WELCOME_TRIAL->value => WhatsAppMessages::where('type',  WhatsAppMessageType::WELCOME_TRIAL)->value('message'),
            WhatsAppMessageType::WELCOME_SUBSCRIPTION->value => WhatsAppMessages::where('type',  WhatsAppMessageType::WELCOME_SUBSCRIPTION)->value('message'),
            WhatsAppMessageType::WELCOME_TUTOR->value => WhatsAppMessages::where('type',  WhatsAppMessageType::WELCOME_TUTOR)->value('message'),

        ]);
    }
    public function schema(): array|Closure
    {
        return [


            Tabs::make('Settings')
                ->schema([
                    WhatsAppSettingsTab::getTab(),
                    Tabs\Tab::make(__('filament-panels.setting.general'))
                        ->schema([
                            TextInput::make('admin.password')
                                ->label(__('filament-panels.setting.password'))
                                ->minLength(4)
                                ->maxLength(255)
                                ->dehydrateStateUsing(fn($state) => $state ? bcrypt($state) : null)
                                ->afterStateHydrated(fn($component) => $component->state(null))
                                ->password(),

                            TextInput::make('password_confirm')
                                ->label(__('filament-panels.setting.password_confirm'))
                                ->password()
                                ->dehydrated(false)
                                ->same('admin.password')
                        ]),

                ])
        ];
    }
    protected function afterSave(): void
    {
        $data = $this->form->getState();
        // Save each message to the whatsapp_messages table by key
        foreach (
            [
                WhatsAppMessageType::WELCOME->value,
                WhatsAppMessageType::WELCOME_TRIAL->value,
                WhatsAppMessageType::WELCOME_SUBSCRIPTION->value,
                WhatsAppMessageType::WELCOME_TUTOR->value,

            ] as $key
        ) {
            WhatsAppMessages::updateOrCreate(
                ['type' => $key],
                ['message' => $data[$key] ?? '']
            );
            Setting::where('key', $key)->delete();
        }

        $user = Auth::user();
        $password = Setting::get('admin.password');

        if ($password) {
            User::where('id', $user->id)->update([
                'password' => $password,
            ]);

            Setting::where('key', 'admin.password')->delete();
        }
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.setting.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.setting.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.setting.title');
    }

    public function getTitle(): string
    {
        return __('الإعدادات العامة');
    }
}
