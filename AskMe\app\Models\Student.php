<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Student extends Model
{

    protected $fillable = ['name', 'number','status'];
    protected $casts = [
        'id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function subscriptions(): HasMany { return $this->hasMany(Subscription::class); }
    public function questions(): HasMany { return $this->hasMany(Question::class); }
    public function comments(): HasMany { return $this->hasMany(StudentComment::class); }
    public function transactions(): HasMany { return $this->hasMany(Transaction::class); }}
