<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subject extends Model
{

    protected $fillable = ['name'];

    protected $casts = [ 'id' => 'integer' ];

    public function tutorSubjects(): Has<PERSON><PERSON> { return $this->hasMany(TutorSubject::class); }
    public function session(): HasMany { return $this->hasMany(UserQuestionSession::class); }
    public function questions(): HasMany { return $this->hasMany(Question::class); }
}
