<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TransactionStatus;
use App\Models\Subscription;
use App\Models\SubscriptionPackage;
use App\Models\Transaction;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class AdminFinanceStats extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $totalRevenue = Transaction::where('status', TransactionStatus::COMPLETED)->sum('amount');
        $monthlyRevenue = Transaction::where('status', TransactionStatus::COMPLETED)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');

        $totalSubscriptions = Subscription::count();

        $activeSubscribers = Subscription::whereColumn('total_questions', '>', 'used_questions')->count();

        $popularPackage = SubscriptionPackage::withCount('subscriptions')
            ->orderByDesc('subscriptions_count')
            ->first();

        $todayRevenue = Transaction::where('status', 'PAID')
            ->whereDate('created_at', now())
            ->sum('amount');

        $startOfMonth = now()->startOfMonth();
        $endOfToday = now()->endOfDay();

        $daysInMonth = collect();
        for ($date = $startOfMonth->copy(); $date->lte($endOfToday); $date->addDay()) {
            $dailyRevenue = Transaction::where('status', TransactionStatus::COMPLETED)
                ->whereDate('created_at', $date)
                ->sum('amount');

            $daysInMonth->push(round($dailyRevenue, 2));
        }

        return [
            Stat::make(__('filament-panels.dashboard.total_revenue'), number_format($totalRevenue, 2) . ' د.ل')
                ->icon('heroicon-o-banknotes')
                ->color('success'),

            Stat::make(__('filament-panels.dashboard.monthly_revenue'), number_format($monthlyRevenue, 2) . ' د.ل')
                ->icon('heroicon-o-calendar')
                ->color('info')
                ->chart($daysInMonth->toArray()),

            Stat::make(__('filament-panels.dashboard.today_revenue'), number_format($todayRevenue, 2) . ' د.ل')
                ->description(__('filament-panels.dashboard.today_revenue_desc'))
                ->icon('heroicon-o-currency-dollar')
                ->color('primary'),

            Stat::make(__('filament-panels.dashboard.total_subscriptions'), $totalSubscriptions)
                ->icon('heroicon-o-archive-box')
                ->color('primary'),

            Stat::make(__('filament-panels.dashboard.active_subscribers'), $activeSubscribers)
                ->description(__('filament-panels.dashboard.active_subscribers_desc'))
                ->icon('heroicon-o-user-group')
                ->color('success'),

            Stat::make(__('filament-panels.dashboard.top_package'), $popularPackage?->name ?? '—')
                ->description(__('filament-panels.dashboard.top_package_desc'))
                ->icon('heroicon-o-star')
                ->color('warning'),
        ];
    }
    protected function getTrend(callable $queryCallback): array
    {
        $trend = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->startOfDay();
            $trend[] = $queryCallback($date);
        }

        return $trend;
    }
}
