<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>تقرير اداء المبيعات</title>
    <style>
        body { 
            font-family: xbriyaz;
            margin: 20px; 
            font-size: 12px; 
            color: #333;
        }
        h1 { 
            color: #2563eb; 
            text-align: center; 
            margin-bottom: 20px; 
            font-size: 24px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        h2 { 
            color: #1e40af; 
            margin-top: 25px; 
            margin-bottom: 15px; 
            border-bottom: 1px solid #e0e0e0; 
            padding-bottom: 8px; 
            font-size: 18px; 
        }
        .summary { 
            background-color: #f8fafc; 
            padding: 15px; 
            border-radius: 8px; 
            margin-bottom: 25px;
            border: 1px solid #e2e8f0;
        }
        .summary p { 
            margin: 0; 
            line-height: 1.6; 
            color: #334155; 
        }
        .metrics-container {
            display: block;
            margin-bottom: 25px;
        }
        .metric-card {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .metric-card p { margin: 0; }
        .metric-name { 
            font-size: 13px; 
            color: #475569; 
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .metric-value { 
            font-size: 24px; 
            color: #1e293b; 
            font-weight: bold; 
            margin: 8px 0; 
        }
        .metric-change { 
            font-size: 12px; 
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .trend-up { color: #059669; }
        .trend-down { color: #dc2626; }
        .trend-neutral { color: #6b7280; }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-bottom: 25px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        th, td { 
            border: 1px solid #e2e8f0; 
            padding: 10px; 
            text-align: left; 
        }
        th { 
            background-color: #f1f5f9; 
            color: #334155; 
            font-weight: bold; 
            font-size: 12px; 
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        tr:nth-child(even) { background-color: #f8fafc; }
        tr:hover { background-color: #f1f5f9; }
        .footer { 
            text-align: center; 
            margin-top: 40px; 
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            font-size: 11px; 
            color: #64748b; 
        }
        .currency { 
            font-family: xbriyaz;
        }
    </style>
</head>
<body>
    <h1>{{ $reportData['title'] }}</h1>
    <div class="summary">
        <p><strong>تاريخ التقرير:</strong> {{ $reportData['date'] }}</p>
        <p>{{ $reportData['summary'] }}</p>
    </div>

    <h2>المؤشرات الأساسية للأداء</h2>
    <div class="metrics-container">
        @foreach($reportData['metrics'] as $metric)
            <div class="metric-card">
                <p class="metric-name">{{ $metric['name'] }}</p>
                <p class="metric-value">{{ $metric['value'] }}</p>
                <p class="metric-change {{ $metric['trend'] === 'up' ? 'trend-up' : ($metric['trend'] === 'down' ? 'trend-down' : 'trend-neutral') }}">
                    {{ $metric['change'] }}
                    @if($metric['trend'] === 'up')
                        &#9650;
                    @elseif($metric['trend'] === 'down')
                        &#9660;
                    @else
                        &#8212;
                    @endif
                </p>
            </div>
        @endforeach
    </div>

    <h2>الأداء المنتجات المنتجة</h2>
    <table>
        <thead>
            <tr>
                <th>الفئة</th>
                <th style="text-align: right">المبيعات</th>
                <th style="text-align: right">الإيرادات</th>
            </tr>
        </thead>
        <tbody>
            @foreach($reportData['productSales'] as $item)
                <tr>
                    <td>{{ $item['category'] }}</td>
                    <td style="text-align: right">{{ number_format($item['sales']) }}</td>
                    <td style="text-align: right" class="currency">${{ number_format($item['revenue'], 2) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <h2>المنتجات الأداء الأفضل</h2>
    <table>
        <thead>
            <tr>
                <th>المنتج</th>
                <th style="text-align: right">المبيعات</th>
                <th style="text-align: right">الإيرادات</th>
            </tr>
        </thead>
        <tbody>
            @foreach($reportData['topProducts'] as $item)
                <tr>
                    <td>{{ $item['name'] }}</td>
                    <td style="text-align: right">{{ number_format($item['unitsSold']) }}</td>
                    <td style="text-align: right" class="currency">${{ number_format($item['revenue'], 2) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>تم إنشاء التقرير في {{ date('Y-m-d H:i:s') }}</p>
        <p>© {{ date('Y') }} BenSaoud. كل الحقوق محفوظة.</p>
    </div>
</body>
</html>
