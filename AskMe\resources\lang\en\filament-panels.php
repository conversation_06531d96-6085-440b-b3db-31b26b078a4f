
<?php

return [
    'title' => 'Dashboard',
    'actions' => [
        'create' => 'Create',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'view' => 'View',

    ],
    'local' => 'Local',
    'international' => 'International',
    'dashboard' => [
        'answered_avg' => 'Average response time',
        'answered_avg_desc' => 'Average time to answer',

        'pending_count' => 'Number of questions awaiting answers',
        'pending_desc' => 'Questions waiting for your answer',

        'completed_count' => 'Number of answered questions',
        'completed_desc' => 'Total number of questions you answered',

        'total_questions'        => 'Total Questions',
        'total_questions_desc'   => 'All questions submitted',

        'answered_questions'     => 'Answered Questions',
        'answered_questions_desc' => 'Questions that were answered',

        'unanswered_questions'   => 'Unanswered Questions',
        'unanswered_questions_desc' => 'Still waiting for answers',

        'questions_this_week'    => 'Questions This Week',
        'questions_this_week_desc' => 'Submitted in the last 7 days',

        'avg_answer_time'        => 'Avg. Answer Time (mins)',
        'avg_answer_time_desc'   => 'Average time to answer',

        'total_tutors'           => 'Total Tutors',
        'total_tutors_desc'      => 'Registered tutors',

        'accepted_activities'    => 'Accepted',
        'accepted_activities_desc' => 'Questions accepted by tutors',

        'answered_activities'    => 'Answered',
        'answered_activities_desc' => 'Tutor answered questions',

        'edited_activities'      => 'Edited',
        'edited_activities_desc' => 'Edited answers',

        'range_filter' => 'Time Range',
        'range_week' => 'This Week',
        'range_month' => 'This Month',
        'range_year' => 'This Year',

        'top_tutors' => 'Top Tutors (Answered Questions)',

        'total_revenue' => 'Total Revenue',
        'monthly_revenue' => 'This Month',
        'today_revenue' => 'Today\'s Revenue',
        'today_revenue_desc' => 'Total from today',
        'total_subscriptions' => 'Total Subscriptions',
        'active_subscribers' => 'Active Subscribers',
        'active_subscribers_desc' => 'Users with remaining questions',
        'top_package' => 'Top Package',
        'top_package_desc' => 'Most subscribed plan',

        'this_month' => 'This Month',
        'last_month' => 'Last Month',

        'packages_sold' => 'Packages Sold',

        'transaction_status' => 'Transaction Statuses',

        'revenue' => 'Revenue',
        'range_week' => 'This Week',
        'range_month' => 'This Month',
        'range_year' => 'This Year',

        'package_name' => 'Package Name',
        'package_price' => 'Package Price',
        'package_sold' => 'Subscriptions Sold',
        'total_revenue' => 'Total Revenue',

        'rank' => 'Rank',
        'tutor_name' => 'Tutor Name',
        'questions_answered' => 'Answered Questions',
        'avg_answer_time' => 'Avg. Answer Time (mins)',

    ],
    'question' => [
        'title' => 'Questions',
        'singular' => 'Question',
        'plural' => 'Questions',
        'fields' => [
            'id' => 'Question Id',
            'name' => 'Student name',
            'grade' => 'Grade',
            'subject' => 'Subject',
            'comments' => 'Comments',
            'question_image' => 'Question image',
            'question_text' => 'Question text',
            'answer_text' => 'Answer text',
            'not_answered' => 'Not answered',
            'no_image' => 'No image',
            'no_answer_text' => 'No answer text',
            'uploaded_answer' => 'Uploaded answer',
            'created_at' => 'Created at ',
            'updated_at' => 'Updated at',
            'no_activity' => "No activity found for this question"
        ],
        'tabs' => [
            'all' => 'All Questions',
            'answered' => 'Answered',
            'unanswered' => 'Unanswered'
        ],
        'notification' => [
            'success' => 'Answered successfully',
            'error' => 'Failed to upload answer',
            'no_change' => 'No changes made',
            'question_accepted' => 'Question accepted!',


        ],
        'action' => [
            'save' => 'Save',
            'accept' => "Accept",

            'edit_answer' => 'Edit the answer',
            "add_answer" => "Add answer",
            'cancel' => 'Cancel',
            'view' => 'View',
            'download' => 'Download',
            'upload' => 'Upload File (Optional)',
        ],
        'body' => [
            'no_question' => "No Question .",
            'all_answered' => 'All questions have answers'

        ]

    ],
    'comments' => [
        'title' => 'Comments',
        'singular' => 'Comment',
        'plural' => 'Comments',
    ],
    'question_request' => [
        'title' => 'Questions Request',
        'singular' => 'Question Request',
        'plural' => 'No available questions for your subjects/grades',
        'body' => [
            'no_request' => 'لا يوجد طلبات'
        ]

    ],
    'grade' => [
        'title' => 'Grages',
        'singular' => 'Grade',
        'plural' => 'Grades',
    ],
    'subject' => [
        'title' => 'Subjects',
        'singular' => 'Subject',
        'plural' => 'Subjects',
    ],
    'tutor' => [
        'title' => 'Tutors',
        'singular' => 'Tutor',
        'plural' => 'Tutors',
        'fields' => [
            'name' => 'Tutor Name',
            'phone' => 'Phone',
            'username' => 'Username',
            'password' => 'Password',
            'grade_subject' => 'Grade and Subject',
            'status' => 'Status',
            'active' => 'Acitve',
            'disabled' => 'Disabled',
            'info' => 'Tutor Info',

        ],
        'activities' => [
            'accept' => 'Accept the question',
            'answer' => 'Answered',
            'edit' => 'Question edited'
        ]

    ],

    'tutor_activities' => [
        'title' => 'Tutor Activities',
        'singular' => 'Tutor Activity',
        'plural' => 'Tutor Activities',
    ],
    'transactions' => [
        'title' => 'Transactions',
        'singular' => 'Transaction',
        'plural' => 'Transactions',
        'fields' => [
            'name' => 'name',
            'amount' => 'Amount',
            'status' => 'Status',
            'payment_provider' => 'Payment Provider',
            'created_at' => 'Created at ',
            'updated_at' => 'Updated at',
        ],
        'totalAmount' => 'Total amount',
        'TotalTransactions' => 'Total Transactions',
        'pending' => 'Pending',
        'paid' => 'Paid',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled'
    ],
    'subscription_pachage' => [
        'title' => 'Subscription Pachages',
        'singular' => 'Subscription Pachage',
        'plural' => 'Subscription Pachages',
        'fields' => [
            'name' => 'Package Name',
            'question_limit' => 'Number of Questions',
            'price' => 'Subscription Price',
            'type' => 'Payment Provider',
            'status' => 'Package Status',
        ]
    ],

    'setting' => [
        'title' => 'Settings',
        'singular' => 'Setting',
        'plural' => 'Settings',
        'templateInfo' => "Download product template file for Meta WhatsApp Catalog.",
        'general' => 'General',
        'subjects' => 'Subjects',
        'active' => 'Activate My Account',
        'password' => "Password",
        'password_confirm' => "Confirm Password",
        'seved' => 'Settings saved successfully.',

        'whatsApp_setting' => 'WhatsApp Settings',

        'whatsApp_welcome' => 'WhatsApp welcome message for unsubscribed students',
        'whatsApp_welcome_desc' => 'Sent to all users upon first interaction.',

        'whatsApp_welcome_sub' => 'WhatsApp welcome message for subscribed students',
        'whatsApp_welcome_sub_desc' => 'Sent to users when no subscription is required.',

        'whatsApp_welcome_free' => 'WhatsApp welcome message for students in the trial period',
        'whatsApp_welcome_free_desc' => 'Sent only to subscribed users.',

        'whatsApp_welcome_tutor' => 'WhatsApp welcome message for tutors',
        'whatsApp_welcome_tutor_desc' => 'Sent to tutors upon any interaction.',


        'subscription_required' => 'Subscription required',
        'subscription_required_desc' => 'If enabled, only subscribed users can ask questions.',

        'question_limit' => 'Number of questions',
        'question_limit_desc' => 'Set the daily limit of free questions allowed for users.'


    ]
];
