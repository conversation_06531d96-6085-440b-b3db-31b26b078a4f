<?php

namespace App\Filament\Widgets;

use App\Enums\OrderStatus;
use App\Models\OrderItems;
use App\Models\Orders;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

/**
 * Class StatsOverview
 *
 * This Filament Stats Overview Widget displays key statistics related to orders.
 * It provides a summary of total orders, pending orders, and rejected orders.
 * It also includes a trend indicator and a mini-chart for total orders.
 */
class StatsOverview extends BaseWidget
{
    /**
     * @var int|null The sort order for this widget in the Filament dashboard.
     * Lower numbers appear earlier.
     */
    protected static ?int $sort = 1;

    /**
     * Get the heading (title) for the stats overview widget.
     *
     * @return string The translated heading for the widget.
     */
    public function getHeading(): string
    {
        return __('filament-panels.orders.title');
    }

    /**
     * Defines the statistics to be displayed in the widget.
     *
     * @return array An array of Stat objects, each representing a single statistic.
     */
    protected function getStats(): array
    {
        // Query to get the count of orders grouped by their creation date.
        $orders = Orders::select('created_at', DB::raw('COUNT(*) as count'))
            ->groupBy('created_at')
            ->orderBy('created_at', 'asc')
            ->pluck('count')
            ->toArray();

        // Get the order count for today (last element) and yesterday (second to last element).
        $today = $orders[0] ?? 0; // Assuming the first element is today's count based on the query structure
        $yesterday = $orders[1] ?? 0; // Assuming the second element is yesterday's count

        // Initialize default icon and color for the trend indicator.
        $icon = 'heroicon-o-arrow-trending-up';
        $color = 'success';

        // Determine the trend based on today's orders vs. yesterday's orders.
        if ($today < $yesterday) {
            // If today's orders are less than yesterday's, set a downward trend icon and 'danger' color.
            $icon = 'heroicon-o-arrow-trending-down';
            $color = 'danger';
        } elseif ($today == $yesterday) {
            // If today's orders are equal to yesterday's, set a neutral icon and 'warning' color.
            $icon = 'heroicon-o-minus-circle';
            $color = 'warning';
        }

        // Return an array of Stat objects, each configured with a title, value, description, icon, chart, and color.
        return [
            // Stat for All Orders:
            Stat::make(
                __('filament-panels.ordersItems.action.all'),
                Orders::count()
            )
                ->description(__('filament-panels.dashboard.data.allOrders'))
                ->descriptionIcon($icon)
                ->chart($orders)
                ->color($color),

            // Stat for Pending Orders:
            Stat::make(
                __('filament-panels.ordersItems.action.pending'),
                OrderItems::where('status', OrderStatus::PENDING)->count()
            )
                ->description(__('filament-panels.dashboard.data.pending'))
                ->chart(
                    OrderItems::where('status', OrderStatus::PENDING)
                        ->select('created_at', DB::raw('COUNT(*) as count'))
                        ->groupBy('created_at')
                        ->orderBy('created_at', 'asc')
                        ->pluck('count')
                        ->toArray()
                )->color('warning'),

            // Stat for Rejected Orders:
            Stat::make(
                __('filament-panels.ordersItems.action.rejected'),
                OrderItems::where('status', OrderStatus::REJECTED)->count()
            )
                ->description(__('filament-panels.dashboard.data.rejected'))
                ->chart(
                    // Mini-chart data for rejected orders, fetched similarly.
                    OrderItems::where('status', OrderStatus::REJECTED)
                        ->select('created_at', DB::raw('COUNT(*) as count'))
                        ->groupBy('created_at')
                        ->orderBy('created_at', 'asc')
                        ->pluck('count')
                        ->toArray()
                )->color('danger'),
        ];
    }
}
