@php
use App\Enums\TutorActivities;

$activities = $getState() ?? collect();
@endphp

@if ($activities->isNotEmpty())
<div class="space-y-4">
    @foreach ($activities as $activity)
    @php

        $actionEnum = is_string($activity->action)
            ? TutorActivities::tryFrom($activity->action)
            : $activity->action;

        $label = $actionEnum?->label() ?? 'Unknown';
        $color = $actionEnum?->color() ?? 'gray';
    @endphp

    
    <div class="rounded-md border p-4 shadow-sm bg-white">
        <div class="text-sm text-gray-600">
        <x-filament::badge color="{{ $color }}">
        {{ $label }}
    </x-filament::badge>            <span class="mx-2">•</span>
            <span>{{ optional($activity->created_at)->format('Y-m-d H:i') }}</span>
        </div>
    </div>
    @endforeach
</div>
@else
<p class="text-sm text-gray-500">{{__('filament-panels.question.fields.no_activity')}}</p>
@endif