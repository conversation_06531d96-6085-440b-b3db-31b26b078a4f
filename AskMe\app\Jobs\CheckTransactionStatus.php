<?php

// namespace App\Jobs;

// use App\Models\Transaction;
// use App\Models\Subscription;
// use App\Enums\TransactionStatus;
// use App\Mail\sendSubscription;
// use Illuminate\Bus\Queueable;
// use Illuminate\Contracts\Queue\ShouldQueue;
// use Illuminate\Foundation\Bus\Dispatchable;
// use Illuminate\Queue\InteractsWithQueue;
// use Illuminate\Queue\SerializesModels;
// use Illuminate\Support\Facades\Http;
// use Illuminate\Support\Facades\Log;
// use Illuminate\Support\Facades\Mail;
// use App\Jobs\SendEmail;
// use App\Jobs\SendText;

// /**
//  * CheckTransactionStatus job checks the status of a payment transaction with T-lync payment provider
//  * and updates the transaction status accordingly. If successful, sends credentials to customer.
//  *
//  * The job will retry up to 12 times with 5 minute intervals between attempts if the API check fails.
//  * This allows sufficient time for payment processing while ensuring customers receive credentials
//  * once payment is confirmed.
//  */

// class CheckTransactionStatus implements ShouldQueue
// {
//     use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

//     public $tries = 12; // Maximum number of attempts
//     public $backoff = 300; // 5 minutes delay between attempts
//     private $transaction;

//     public function __construct(Transaction $transaction)
//     {
//         $this->transaction = $transaction;
//     }

//     public function handle()
//     {
//         try {
//             // Prepare payload for T-lync API request
//             $payload = [
//                 'store_id' => config('api.store_id'),
//                 'custom_ref' => $this->transaction->uuid
//             ];

//             // Make API request to T-lync to verify transaction status
//             $response = Http::withHeaders(['Accept' => 'application/json'])
//                             ->withToken(config('api.api_token'))
//                             ->post(config('api.get_transaction_receipt_api_live'), $payload);

//             Log::info('Response in job: -> ', [$response->json()]);
//             // If API request fails, log error and schedule retry
//             if (!$response->successful()) {
//                 Log::error('Failed to check transaction status', [
//                     'transaction_id' => $this->transaction->id,
//                     'response' => $response->body()
//                 ]);

//                 $this->release($this->backoff); // Schedule retry after 5 minutes
//                 return;
//             }

//             $data = $response->json();

//             // Check if the call-back was successful then return
//             if($this->transaction->status === TransactionStatus::COMPLETED){

//                 return;
//             }

//             // If transaction is successful, update status and send credentials
//             if ($data['result'] === 'success') {

//                 // Update transaction record with completed status and external reference
//                 $this->transaction->update([
                    
//                     'status' => TransactionStatus::COMPLETED,
//                     'external_ref' => $data['data']['reference'],
//                 ]);

//                 if($this->transaction->email !== ''){
                    
//                     // Send subscription via email if email exists
//                     SendEmail::dispatch($this->transaction);
                    
//                 }

//                 // Send subscription credentials via WhatsApp
//                  $accountData=[];
//                  $accountData[] = $this->transaction->subscription->email;
//                  $accountData[] = $this->transaction->subscription->password;
//                  SendText::dispatch($this->transaction->phone, 'sendAccountData',$accountData);

//                 return;
//             }

//             // If transaction is not completed, schedule a retry after 5 minutes
//             $this->release($this->backoff);

//         } catch (\Exception $e) {
//             // Log any unexpected errors and schedule retry
//             Log::error('Error checking transaction status: ' . $e->getMessage());
//             $this->release($this->backoff);
//         }
//     }
// }
