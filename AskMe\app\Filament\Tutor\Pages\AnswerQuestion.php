<?php

namespace App\Filament\Tutor\Pages;

use App\Enums\AnswerType;
use App\Enums\TutorActivities;
use App\Models\Question;
use App\Models\TutorActivity;
use App\Services\TutorService;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class AnswerQuestion extends Page implements HasForms
{
    use InteractsWithForms;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $title = ' ';
    public array $data = [];
    protected static string $view = 'filament.pages.answer-question';
    public ?Question $question = null;



    public static function getSlug(): string
    {
        return 'answer-question/{record}';
    }

    public function mount(Question $record): void
    {
        $this->question = $record;
        $this->data = [
            'answer_text' => $record->answer_text,
        ];
    }


    public function form(Form $form): Form
    {
        return $form->statePath('data')
            ->schema([
                Textarea::make('answer_text')
                    ->label(__('filament-panels.question.fields.answer_text'))
                    ->required()
                    ->maxLength(1000),

                FileUpload::make('answer_url')
                    ->label(__('filament-panels.question.action.upload'))
                    ->directory('answers')
                    ->disk('public')
                    ->visibility('public')
                    ->acceptedFileTypes(['application/pdf', 'video/mp4', 'image/jpeg', 'image/png', 'image/gif'])
                    ->previewable(true)
            ]);
    }


    public function submit(): void
    {
        $data = $this->data;
        $file = $data['answer_url'] ?? null;

        if (is_array($file) && reset($file) instanceof TemporaryUploadedFile) {
            $uploadedFile = reset($file);

            //Delete old file
            if ($this->question->answer_url && Storage::disk('public')->exists(str_replace('/storage/', '', $this->question->answer_url))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $this->question->answer_url));
            }

            // Save new file
            $storedPath = $uploadedFile->store('answers', 'public');
            $data['answer_url'] = '/storage/' . $storedPath;
        } elseif ($file instanceof TemporaryUploadedFile) {

            if ($this->question->answer_url && Storage::disk('public')->exists(str_replace('/storage/', '', $this->question->answer_url))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $this->question->answer_url));
            }

            $storedPath = $file->store('answers', 'public');
            $data['answer_url'] = '/storage/' . $storedPath;
        } else {

            // Keep existing file if no new upload
            $data['answer_url'] = $this->question->answer_url;
        }
        // Check for changes
        $newText = trim($data['answer_text'] ?? '');
        $oldText = trim($this->question->answer_text ?? '');
        $oldUrl = $this->question->answer_url;

        $isTextChanged = $newText !== $oldText;
        $isFileChanged = $oldUrl !==  $data['answer_url'];

        if (! $isTextChanged && ! $isFileChanged) {
            Notification::make()
                ->title(__('filament-panels.question.notification.no_change'))
                ->info()
                ->send();

            return;
        } elseif ($isFileChanged) {
            $extension = strtolower(pathinfo($data['answer_url'], PATHINFO_EXTENSION));
            $answerType = match ($extension) {
                'jpg', 'jpeg', 'png', 'gif' => AnswerType::Image,
                'mp4'                       => AnswerType::Video,
                'pdf'                       => AnswerType::Document,
                default                     => null,
            };
        } else {
            $answerType = $this->question->answer_type;
        }
        $updated = $this->question->update([
            'answer_text' => $data['answer_text'] ?? null,
            'answer_url'  => $data['answer_url'] ?? null,
            'answer_type'  => $answerType,
        ]);

        Notification::make()
            ->title($updated ? __('filament-panels.question.notification.success') : __('filament-panels.question.notification.error'))
            ->{$updated ? 'success' : 'danger'}()
            ->duration(3000)
            ->send();

        if ($updated) {
            app(TutorService::class)->tutorActivities($this->question);
            app(TutorService::class)->sendAnswerTOStudent($this->question->student->number, $this->question);
            $this->redirect('/tutor/tutor-questions');
        }
    }
}
