<?php

namespace App\Filament\Admin\Pages;

use App\Enums\WhatsAppMessageType;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;

class WhatsAppSettingsTab
{
    public static function getTab(): Tab
    {
        return 
            Tab::make(__('filament-panels.setting.whatsApp_setting'))
                ->schema([
                    Group::make()
                        ->schema([
                            Toggle::make('subscription_required')
                                ->label(__('filament-panels.setting.subscription_required'))
                                ->helperText(__('filament-panels.setting.subscription_required_desc'))
                                ->default(true),

                            TextInput::make('question_limit')
                                ->label(__('filament-panels.setting.question_limit'))
                                ->hint('-1 means unlimited questions per day')
                                ->numeric()
                                ->visible(fn($get) => !$get('subscription_required'))
                                ->default(3)
                                ->helperText(__('filament-panels.setting.question_limit_desc')),

                            Textarea::make(WhatsAppMessageType::WELCOME->value)
                                ->label(__('filament-panels.setting.whatsApp_welcome'))
                                ->rows(2)
                                ->helperText(__('filament-panels.setting.whatsApp_welcome_desc')),

                            Textarea::make(WhatsAppMessageType::WELCOME_TRIAL->value)
                                ->label(__('filament-panels.setting.whatsApp_welcome_free'))
                                ->rows(2)
                                ->helperText(__('filament-panels.setting.whatsApp_welcome_free_desc')),

                            Textarea::make(WhatsAppMessageType::WELCOME_SUBSCRIPTION->value)
                                ->label(__('filament-panels.setting.whatsApp_welcome_sub'))
                                ->rows(2)
                                ->helperText(__('filament-panels.setting.whatsApp_welcome_sub')),

                            Textarea::make(WhatsAppMessageType::WELCOME_TUTOR->value)
                                ->label(__('filament-panels.setting.whatsApp_welcome_tutor'))
                                ->rows(2)
                                ->helperText(__('filament-panels.setting.whatsApp_welcome_tutor_desc')),
                        ])
                        ->columns(1),
                        ]);
                       
    }

    public static function getSortOrder(): int
    {
        return 1;
    }
}
