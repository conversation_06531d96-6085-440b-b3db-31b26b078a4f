<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TransactionStatus;
use App\Models\Transaction;
use Filament\Widgets\ChartWidget;

class RevenueComparisonChart extends ChartWidget
{
    protected static ?string $heading = 'مقارنة الإيرادات (هذا الشهر مقابل الشهر الماضي)';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';
    // Max chart height
    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        $thisMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        $thisMonthData = collect();
        $lastMonthData = collect();
        $labels = [];

        for ($day = 1; $day <= 31; $day++) {
            $thisDate = $thisMonth->copy()->setDay($day);
            $lastDate = $lastMonth->copy()->setDay($day);

            // Stop loop if day exceeds actual days in the month
            if (!$thisDate->isSameMonth($thisMonth) || !$lastDate->isSameMonth($lastMonth)) {
                break;
            }

            $labels[] = (string) $day;

            $thisRevenue = Transaction::where('status', TransactionStatus::COMPLETED)
                ->whereDate('created_at', $thisDate)
                ->sum('amount');

            $lastRevenue = Transaction::where('status', TransactionStatus::COMPLETED)
                ->whereDate('created_at', $lastDate)
                ->sum('amount');

            $thisMonthData->push(round($thisRevenue, 2));
            $lastMonthData->push(round($lastRevenue, 2));
        }

        return [
            'datasets' => [
                [
                    'label' => __('filament-panels.dashboard.this_month'),
                    'data' => $thisMonthData,
                    'borderColor' => '#3b82f6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
                [
                    'label' => __('filament-panels.dashboard.last_month'),
                    'data' => $lastMonthData,
                    'borderColor' => '#f97316',
                    'backgroundColor' => 'rgba(249, 115, 22, 0.1)',
                    'fill' => true,
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
