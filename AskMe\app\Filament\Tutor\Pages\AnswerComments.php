<?php

namespace App\Filament\Tutor\Pages;

use App\Models\Question;
use Filament\Pages\Page;

class AnswerComments extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center-text';

    protected static string $view = 'filament.pages.answer-comments';
    protected static bool $shouldRegisterNavigation = true;

    public function getTitle(): string
    {
        return __('filament-panels.comments.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.comments.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.comments.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.comments.title');
    }

    public function getViewData(): array
{
    return [
        'questions' => Question::with('comments')->whereHas('comments')->latest()->get(),
    ];
}

}
