<?php

namespace App\Filament\Settings\Forms;

use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;

class AppSettingsTab
{
    public static function getTab(): Tab
    {
        return  Tab::make('general')->label(__('filament-panels.setting.general'))
            ->icon('heroicon-o-cog-6-tooth')
            ->schema([
                TextInput::make('general.app_name_ar')
                    ->label(__('filament-panels.setting.nameAr'))
                    ->required(),

                TextInput::make('general.app_name_en')
                    ->label(__('filament-panels.setting.nameEn'))
                    ->required(),



                TextInput::make('general.password')
                    ->label(__('filament-panels.setting.password'))
                    ->password()
                    ->minLength(4)
                    ->maxLength(255)
                    ->dehydrateStateUsing(fn($state) => $state ? bcrypt($state) : null)
                    ->afterStateHydrated(fn($component) => $component->state(null)),

                TextInput::make('general.phone')
                    ->label(__('filament-panels.setting.phone'))
                    ->required()
                    ->hint('for testing set it to 0')
                    ->tel(),

                TextInput::make('general.welcome_message')
                    ->label(__('filament-panels.setting.welcomeMessage')),

                FileUpload::make('general.logo')
                    ->label(__('filament-panels.setting.logo'))
                    ->image()
                    ->directory('settings/logos'),
            ]);
    }

    public static function getSortOrder(): int
    {
        return 1;
    }
}
