<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TutorSubject extends Model
{

    protected $fillable = ['tutor_id', 'subject_id', 'grade_id'];

    protected $casts = [ 'id' => 'integer' ];

    public function tutor(): BelongsTo { return $this->belongsTo(Tutor::class); }
    public function subject(): BelongsTo { return $this->belongsTo(Subject::class); }
    public function grade(): BelongsTo { return $this->belongsTo(Grade::class); }
}
