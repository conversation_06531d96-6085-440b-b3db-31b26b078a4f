<?php

namespace App\Services;

use App\Enums\StudentState;
use App\Enums\SubscriptionPlanType;
use App\Enums\TutorActivities;
use App\Enums\UserState;
use App\Enums\WhatsAppMessageType;
use App\Models\CommentSessions;
use App\Models\Grade;
use App\Models\Question;
use App\Models\Setting;
use App\Models\Student;
use App\Models\StudentComment;
use App\Models\Subject;
use App\Models\Subscription;
use App\Models\SubscriptionPackage;
use App\Models\Tutor;
use App\Models\TutorActivity;
use App\Models\TutorSubject;
use App\Models\UserQuestionSession;
use App\Models\WhatsAppMessages;
use App\Services\SelectionService;
use App\Services\TutorService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class QuestionService
{
    protected  $whatsAppService;
    protected  $tutorService;
    protected $mediaService;

    public function __construct(
        WhatsAppService $whatsAppService,
        TutorService $tutorService,
        MediaService $mediaService
    ) {
        $this->whatsAppService = $whatsAppService;
        $this->tutorService = $tutorService;
        $this->mediaService = $mediaService;
    }

    /**
     * Send a list of available grades to the student 
     *
     * @param string $to WhatsApp number of the student
     */
    public function sendGradeMessage(string $to): void
    {
        $studentId = Student::where('number', $to)->first()->id;
        $questionCount = Question::where('student_id', $studentId)
            ->whereDate('created_at', Carbon::today())
            ->count();
        $settingQuestionLimit = Setting::get('question_limit') ?? 1;
        Log::info("questionCount: ", [$questionCount]);
        Log::info("settingQuestionLimit: ", [$settingQuestionLimit]);

        if ($settingQuestionLimit != -1 && $questionCount === $settingQuestionLimit) {
            $this->whatsAppService->sendText($to, "لقد قمت بطرح {$settingQuestionLimit} أسئلة اليوم. لا يمكنك طرح المزيد من الأسئلة.");
            return;
        }
        //Fetch grades that have at least one associated tutor subject
        $grades = Grade::whereHas('tutorSubjects')->get();
        if ($grades->isEmpty()) {
            $this->whatsAppService->sendText($to, "لا يوجد صفوف دراسية متاحة.");
            return;
        }
        $sections = [
            [
                'title' => 'الصفوف الدراسية',
            ]
        ];
        //Format each grade as a row for the WhatsApp list message

        $sections[0]['rows'] = $grades->map(fn($grade) => [
            'id' => "grade_{$grade->id}",
            'title' => $grade->name,
        ])->toArray();
        Log::info("rows: ", [$sections]);

        $this->whatsAppService->sendList(
            $to,
            '*اختر الصف الدراسي*',
            'عرض الصفوف الدراسية',
            $sections
        );
    }


    /**
     * Send a list of subjects for the selected grade
     *
     * @param string $to WhatsApp number of the student
     * @param int $gradeId ID of the selected grade
     */
    public function sendSubjectMessage(string $to, int $gradeId): void
    {
        //Fetch subjects that have at least one associated tutor for the given grade
        $subjects = Subject::whereHas('tutorSubjects', function ($query) use ($gradeId) {
            $query->where('grade_id', $gradeId)
                ->whereNotNull('tutor_id');
        })->get();
        $sections = [
            [
                'title' =>   'المادة الدراسية',
            ]
        ];

        //Format each subject as a row
        $sections[0]['rows'] = $subjects->map(fn($subject) => [
            'id' => "subject_{$subject->id}_{$gradeId}",
            'title' => $subject->name,
        ])->toArray();

        $this->whatsAppService->sendList(
            $to,
            '*اختر المادة الدراسية*',
            'عرض المواد الدراسية',
            $sections
        );
    }


    /**
     * Save the selected grade and subject, update the student's state,
     * and prompt them to send their question.
     *
     * @param string $to WhatsApp number of the student
     * @param string $selection Selection string in the format subject_{subjectId}_{gradeId}
     */
    public function sendQuestionMessage(string $to, string $selection): void
    {
        //Find the student by WhatsApp number
        $student = Student::where('number', $to)->first();
        $questionCount = Question::where('student_id', $student->id)->where('created_at', Carbon::now())->count();
        $settingQuestionLimit = Setting::get('question_limit') ?? -1;
        if ($settingQuestionLimit != -1 && $questionCount === $settingQuestionLimit) {
            $this->whatsAppService->sendText($to, "لقد قمت بطرح {$settingQuestionLimit} أسئلة اليوم. لا يمكنك طرح المزيد من الأسئلة.");
            return;
        }

        //Update student status to indicate they're about to send a question
        $student->update(['status' => UserState::AwaitingQuestion]);

        [$_, $subjectId, $gradeId] = explode('_', $selection);

        UserQuestionSession::updateOrCreate(
            ['whatsapp_number' => $to],
            ['subject_id' => $subjectId, 'grade_id' => $gradeId]
        );

        $this->whatsAppService->sendText(
            $to,
            "*يرجى كتابة سؤالك بالتفصيل لتتم مساعدتك بشكل افضل او يمكنك إرسال صورة للسؤال.*"
        );
    }

    /**
     * Handle the student's submitted question (text or image)
     *
     * @param array $message The WhatsApp message payload
     */
    public function handleQuestionSubmission(array $message): void
    {
        $from = $message['from'] ?? null;
        $type = $message['type'] ?? null;
        $name = $message['contacts'][0]['profile']['name'] ?? 'Unknown';

        $student = Student::firstOrCreate(['number' => $from], ['name' => $name]);
        $questionCount = Question::where('student_id', $student->id)->where('created_at', Carbon::now())->count();
        $settingQuestionLimit = Setting::get('question_limit') ?? -1;
        if ($settingQuestionLimit != -1 && $questionCount === $settingQuestionLimit) {
            $this->whatsAppService->sendText($from, "لقد قمت بطرح {$settingQuestionLimit} أسئلة اليوم. لا يمكنك طرح المزيد من الأسئلة.");
            return;
        }

        // Get current question session
        $session = UserQuestionSession::where('whatsapp_number', $from)->first();

        if (!$session || !$session->grade_id || !$session->subject_id) {
            $this->whatsAppService->sendText($from, "يرجى اختيار الصف والمادة اولا.");
            return;
        }

        $isAubscriptionRequier = Setting::get('is_subscription_required') ?? 0;
        $activeSubscription = $student->subscriptions()
            ->whereRaw('total_questions > used_questions')
            ->latest()
            ->first();
        if ($isAubscriptionRequier && !$activeSubscription) {

            $this->whatsAppService->sendText($from, "لا يوجد اشتراك نشط. الرجاء الاشتراك لإرسال الأسئلة.");
            return;
        }

        // Only proceed if it's a supported message type
        if ($type === 'image' || $type === 'text') {
            // Get media details (image or text content)
            $mediaList = $message['image'] ?? [];
            Log::info("mediaList: ", [$mediaList]);

            $imageId = $mediaList['id'] ?? null;
            $caption = $type === 'image'
                ? $message['image']['caption'] ?? ''
                : $message['text']['body'] ?? '';

            $imagePath = $imageId ? $this->mediaService->getMediaAndSave($imageId) : null;

            try {
                // Save question in the database
                $question = Question::create([
                    'student_id'   => $student->id,
                    'grade_id'     => $session->grade_id,
                    'subject_id'   => $session->subject_id,
                    'question_text' => $caption,
                    'image_url'    => $imagePath,
                    'image_id'     => $imageId,
                ]);
            } catch (\Exception $e) {
                Log::error('Error creating question', ['error' => $e->getMessage()]);
                $this->whatsAppService->sendText($from, "حدث خطأ أثناء إرسال السؤال. يرجى المحاولة مرة تانية.");
                return;
            }

            if ($isAubscriptionRequier && !$activeSubscription) {
                // Update usage 
                $activeSubscription->increment('used_questions');
            }

            $session->delete();
            $student->update(['status' => UserState::Init]);

            $this->whatsAppService->sendText($from, "تم استلام سؤالك  وسيتم الرد عليه قريباً.");
            $this->sendQuestionToTutors($question);
            return;
        }

        // Fallback for unsupported message types
        $this->whatsAppService->sendText($from, "نوع الرسالة غير مدعوم. يرجى إرسال صورة او نص.");
    }

    /**
     * Send a question to all tutors who are assigned to the same grade and subject as the question
     *
     * @param  Question  $question
     * @return void
     */
    public function sendQuestionToTutors($question): void
    {
        // etrieve all tutors who teach the subject and grade of the given question
        $tutors = TutorSubject::with(['tutor' => function ($q) {
            $q->where('user.is_active', true);
        }])
            ->where('grade_id', $question->grade_id)
            ->where('subject_id', $question->subject_id)
            ->get();

        foreach ($tutors as $tutorSubject) {
            $tutor = $tutorSubject->tutor;

            // If the question includes an image, send it to the tutor
            if ($question->image_url) {
                $this->whatsAppService->sendImage(
                    $tutor->phone,
                    $question->image_url,
                    "* صورة مرفقة مع السؤال من الطالب {$question->student->name}*"
                );
            }

            $message = "*سؤال جديد من الطالب {$question->student->name}* \n"
                . "*المادة:* {$question->subject->name} \n"
                . "*الصف:* {$question->grade->name} \n";
            if ($question->question_text != '') {
                $message .= "*السؤال:* {$question->question_text} \n\n";
            }
            $message .= "هل ترغب في الإجابة على هذا السؤال؟";
            $message .= "\n\n*يرجى الضغط على الزر أدناه لقبول السؤال.*";

            $this->whatsAppService->sendButtons(
                $tutor->phone,
                $message,
                [
                    [
                        'type' => 'reply',
                        'reply' => [
                            'id' => "accept_{$question->id}",
                            'title' => 'قبول السؤال'
                        ]
                    ],
                ]
            );
        }
    }

    /**
     * Allows a tutor to accept a question by its ID.
     *
     * @param string $tutorNumber The phone number of the tutor
     * @param int $questionId The ID of the question to be accepted
     * @return void
     */
    public function acceptQuestion($tutorNumber, $questionId): void
    {
        $question = Question::where('id', $questionId)->lockForUpdate()->first();

        // If the question doesn't exist
        if (!$question) {
            $this->whatsAppService->sendText($tutorNumber, "السؤال غير موجود.");
            Log::error('Question not found', ['questionId' => $questionId]);
            return;
        }

        // If the question has already been claimed by another tutor
        if ($question->tutor_id) {
            $this->whatsAppService->sendText($tutorNumber, "تم قبول هذا السؤال من قبل مدرس آخر.");
            TutorActivity::create([
                'tutor_id' => $question->tutor_id,
                'question_id' => $question->id,
                'activity_type' =>  TutorActivities::Accept
            ]);
            return;
        }

        //Retrieve the tutor using the phone number
        $tutor = Tutor::where("phone", $tutorNumber)->first();

        //Assign the question to the tutor
        $question->update(['tutor_id' => $tutor->id]);

        $this->whatsAppService->sendText($tutorNumber, "*تم قبول السؤال بنجاح.*");
    }

    /**
     * Prepare the student to write a comment on a specific question.
     *
     * @param string $to The student's WhatsApp number
     * @param int $questionId The ID of the question the comment is related to
     * @return void
     */
    public function addComment($to, $questionId)
    {
        //Find the student by WhatsApp number
        $student = Student::where('number', $to)->first();

        //Update student status to indicate they are in the comment writing state
        $student->update(['status' => UserState::AwaitingComment]);

        CommentSessions::updateOrCreate(
            ['whatsapp_number' => $to],
            ['question_id' => $questionId]
        );

        //Prompt the student to write their comment
        $this->whatsAppService->sendText(
            $to,
            "*يرجي كتابة تعليقك*"
        );
    }
    /**
     * Save a student's comment on a question and notify the assigned tutor.
     *
     * @param string $to The recipient number 
     * @param array $message The incoming WhatsApp message payload
     * @return void
     */
    public function saveComment($to, $message): void
    {
        $from = $message['from'] ?? null;
        $type = $message['type'] ?? null;
        $name = $message['contacts'][0]['profile']['name'] ?? 'Unknown';

        //Find or create the student record
        $student = Student::firstOrCreate(['number' => $from], ['name' => $name]);

        //Only handle text messages
        if ($type === 'text') {
            $commentText = $message['text']['body'];

            //Find the student's comment session
            $session = CommentSessions::where('whatsapp_number', $from)->first();
            if (!$session) {
                return;
            }

            try {
                // Save the student's comment in the database
                StudentComment::create([
                    'student_id' => $student->id,
                    'question_id' => $session->question_id,
                    'comment' => $commentText,
                ]);
            } catch (\Exception $e) {
                Log::error('Error creating comment', ['error' => $e->getMessage()]);
                return;
            }

            // Notify the student that their comment was successfully received
            $this->whatsAppService->sendText(
                $from,
                "*تم استلام تعليقك بنجاح.*"
            );

            $student = Student::where('number', $to)->first();
            $student->update(['status' => UserState::Init]);
            $student->save();

            // Delete the comment session
            $session->delete();

            // Forward the comment to the tutor
            $this->sendCommentToTutor($session->question_id, $commentText);
        }
    }


    /**
     * Send the student's comment to the tutor who answered the question.
     *
     * @param int $questionId The ID of the related question
     * @param string $commentText The comment text from the student
     * @return void
     */
    public function sendCommentToTutor($questionId, $commentText): void
    {
        $question = Question::with('tutor', 'student', 'subject', 'grade')->find($questionId);

        if (!$question || !$question->tutor) {
            Log::error('Tutor not found for question', ['questionId' => $questionId]);
            return;
        }

        // Send the comment to the tutor with a button to modify their answer
        $this->whatsAppService->sendButtons(
            $question->tutor->phone,
            "*تعليق جديد من الطالب {$question->student->name}* \n"
                . "*المادة:* {$question->subject->name} \n"
                . "*الصف:* {$question->grade->name} \n"
                . "*التعليق:* {$commentText} \n\n",
            [
                [
                    'type' => 'reply',
                    'reply' => [
                        'id' => "answer_{$questionId}",
                        'title' => 'تعديل الاجابة'
                    ]
                ],
            ]
        );
    }


    /**
     * Send a list of the student's previous questions 
     *
     * @param string $to The student's phone number
     * @return void
     */
    public function sendQuestionList($to): void
    {
        // ind the student using their phone number
        $student = Student::where('number', $to)->first();

        //Get all questions submitted by the student
        $question = Question::where('student_id', $student->id)
            ->orderBy('created_at', 'desc')
            ->get();

        if ($question->isEmpty()) {
            $this->whatsAppService->sendText($to, 'ليس لديك اي اسئلة .');
            return;
        }

        // WhatsApp lists have a max of 10 rows ---- split questions into chunks
        $chunks = $question->chunk(10);
        $part = 1;

        foreach ($chunks as $chunk) {
            $rows = [];

            foreach ($chunk as $sub) {
                $rows[] = [
                    'id' => 'student_view_question_' . $sub->id,
                    'title' => $sub->subject->name,
                    'description' => $sub->subject->name . " - " . $sub->grade->name . " - " . $sub->created_at->format('Y-m-d H:i:s')
                ];
            }

            $this->whatsAppService->sendList(
                $to,
                " *الاسئلة* (جزء{$part})",
                "عرض الاسئلة",
                [[
                    'title' => "اسئلة ",
                    'rows' => $rows
                ]]
            );

            $part++;
        }
    }
    /**
     * Send detailed information about a specific question
     *
     * @param string $to The student's phone number
     * @param int $questionId The ID of the question to display
     * @return void
     */
    public function questionDetails($to, $questionId): void
    {
        // Load the question with its subject
        $question = Question::with('subject')->where('id', $questionId)->first();

        if ($question->image_url) {
            $this->whatsAppService->sendImage(
                $to,
                $question->image_url,
                "* صورة مرفقة مع السؤال *"
            );
        }

        $message =
            "*المادة:* {$question->subject->name} \n"
            . "*الصف:* {$question->grade->name} \n"
            . "*التاريخ:* {$question->created_at->format('Y-m-d H:i:s')} \n\n";

        if ($question->question_text != '') {
            $message .= "*السؤال:* {$question->question_text} \n\n";
        }

        $this->whatsAppService->sendText($to, $message);

        // If an answer exists send it using the tutor service
        if ($question->answer_text || $question->answer_id) {
            $this->tutorService->sendAnswerTOStudent($to, $question);
        }
    }

    /**
     * Send a text message containing all available grades and their related subjects.
     *
     * @param string $to WhatsApp number of the student
     */
    public function viewAvailableGradeAndSubject(string $to): void
    {
        // Fetch all grades with at least one subject that has tutors
        $grades = Grade::whereHas('tutorSubjects')->get();

        if ($grades->isEmpty()) {
            $this->whatsAppService->sendText($to, "لا يوجد صفوف دراسية متاحة حالياً.");
            return;
        }

        $message = "*المواد الدراسية المتاحة:*\n\n";

        foreach ($grades as $grade) {
            // Fetch subjects linked to this grade through TutorSubject
            $subjectIds = TutorSubject::where('grade_id', $grade->id)
                ->pluck('subject_id')
                ->unique();

            if ($subjectIds->isEmpty()) {
                continue;
            }

            $subjectNames = Subject::whereIn('id', $subjectIds)->pluck('name');

            $message .= "🔸 *{$grade->name}*\n";
            foreach ($subjectNames as $subjectName) {
                $message .= "  - {$subjectName}\n";
            }
            $message .= "\n";
        }

        $this->whatsAppService->sendText($to, trim($message));
    }
}
