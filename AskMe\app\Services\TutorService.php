<?php

namespace App\Services;

use App\Enums\AnswerType;
use App\Enums\TutorActivities;
use App\Enums\UserState;
use App\Enums\WhatsAppMessageType;
use App\Models\Question;
use App\Models\Tutor;
use App\Models\TutorActivity;
use App\Models\TutorQuestionSessions;
use App\Models\WhatsAppMessages;
use Illuminate\Support\Facades\Log;

class TutorService
{
    protected  $whatsAppService;
    protected  $mediaService;

    public function __construct(
        WhatsAppService $whatsAppService,
        MediaService $mediaService
    ) {
        $this->whatsAppService = $whatsAppService;
        $this->mediaService = $mediaService;
    }

    /**
     * Send the default welcome message to a tutor with interactive options.
     *
     * @param string $to The tutor's WhatsApp number
     * @return void
     */
    public function defaultMessage($to)
    {
        // Retrieve the pre-defined welcome message for tutors 
        $whatsAppWelcomeMessagesSubscription = WhatsAppMessages::where('type', [WhatsAppMessageType::WELCOME_TUTOR])->first();

        $row = [
            [
                "type" => "reply",
                "reply" => [
                    "id" => "tutor_questions_list",
                    "title" => 'الاجابة على الاسئلة'
                ]
            ],
            [
                "type" => "reply",
                "reply" => [
                    "id" => "tutor_comments_list",
                    "title" => 'تعليقات على الاجابة'
                ]
            ],
        ];

        $message = $whatsAppWelcomeMessagesSubscription->message
            ?? "\n*مرحبًا بك في خدمة المدرسين*\n يرجى الإجابة على الأسئلة في اقرب وقت ممكن واختيار احد الخيارات من القائمة أدناه";

        // Send the welcome message with interactive buttons
        $this->whatsAppService->sendButtons($to, $message, $row);
    }

    /**
     * Send a list of unanswered questions assigned to the tutor.
     *
     * @param string $to The tutor's WhatsApp number
     * @return void
     */
    public function sendQuestionList($to): void
    {
        $tutor = Tutor::where('phone', $to)->first();

        //Retrieve all unanswered questions assigned to the tutor
        $question = Question::where('tutor_id', $tutor->id)
            ->with('student', 'subject', 'grade')
            ->whereNull('answer_text')
            ->orderBy('created_at', 'desc')
            ->get();

        //If the tutor has no pending questions
        if ($question->isEmpty()) {
            $this->whatsAppService->sendText($to, 'ليس لديك اي اسئلة .');
            return;
        }

        // WhatsApp limits list messages to 10 rows ---- split the list into chunks
        $chunks = $question->chunk(10);
        $part = 1;

        foreach ($chunks as $chunk) {
            $rows = [];

            foreach ($chunk as $sub) {
                $rows[] = [
                    'id' => 'tutor_view_question_' . $sub->id,
                    'title' => $sub->student->name,
                    'description' => $sub->subject->name . " - " . $sub->grade->name . " - " . $sub->created_at->format('Y-m-d H:i:s')
                ];
            }

            $this->whatsAppService->sendList(
                $to,
                " *الاسئلة* (جزء{$part})",
                "عرض الاسئلة",
                [[
                    'title' => "اسئلة الطلاب",
                    'rows' => $rows
                ]]
            );

            $part++;
        }
    }

    /**
     * Send detailed information about a student's question to the tutor,
     * including the option to respond.
     *
     * @param string $to The tutor's WhatsApp number
     * @param int $questionId The ID of the question to show
     * @return void
     */
    public function questionDetails($to, $questionId): void
    {
        //Retrieve the question with its related subject
        $question = Question::with('subject', 'student', 'grade')->where('id', $questionId)->first();

        if ($question->image_url) {
            $this->whatsAppService->sendImage(
                $to,
                $question->image_url,
                "* صورة مرفقة مع السؤال من الطالب {$question->student->name}*"
            );
        }

        $message = "*سؤال من الطالب {$question->student->name}* \n"
            . "*المادة:* {$question->subject->name} \n"
            . "*الصف:* {$question->grade->name} \n"
            . "*التاريخ:* {$question->created_at->format('Y-m-d H:i:s')} \n";

        if ($question->question_text != '') {
            $message .= "*السؤال:* {$question->question_text} \n\n";
        }

        $message .= "\n\n*يرجى الضغط على الزر أدناه للاجابة على السؤال.*";

        $this->whatsAppService->sendButtons(
            $to,
            $message,
            [
                [
                    'type' => 'reply',
                    'reply' => [
                        'id' => "answer_{$question->id}",
                        'title' => 'اجابة على السؤال',
                    ]
                ],
            ]
        );
    }

    /**
     * Prepares the tutor to submit an answer for a specific question.
     *
     * @param string $to The tutor's number
     * @param int $questionId The ID of the question the tutor will answer
     * @return void
     */
    public function answerQuestion($to, $questionId): void
    {

        $tutor = Tutor::where('phone', $to)->first();

        //Update the tutor's state to indicate they are preparing to answer
        $tutor->update(['status' => UserState::AwaitingAnswer]);

        // ave or update the tutor's session with the current question
        TutorQuestionSessions::updateOrCreate(
            ['whatsapp_number' => $to],
            ['question_id' => $questionId]
        );

        //Prompt the tutor to send their answer with supported formats
        $this->whatsAppService->sendText(
            $to,
            "*الرجاء إرسال الإجابة. يمكنك إرسالها كنص، او كملف PDF، أو كصورة، أو كفيديو. إذا كنت سترسل صورة او فيديو، يرجى إضافة وصف توضيحي مع الملف.*"
        );
    }

    /**
     * Save the tutor's answer  from the incoming WhatsApp message
     *
     * @param string $from The tutor's WhatsApp number
     * @param array $message The payload received from WhatsApp
     * @return void
     */
    public function saveTutorAnswer(string $from, array $message): void
    {
        $tutor = Tutor::where('phone', $from)->first();
        Log::info('saveTutorAnswer', [$tutor]);

        $session = TutorQuestionSessions::where('whatsapp_number', $from)->first();

        // If no session is found
        if (!$session) {
            $this->whatsAppService->sendText($from, "لا يوجد سؤال للرد عليه حالياً");
            return;
        }

        //Determine the type of incoming message
        $type = $message['type'] ?? null;

        switch ($type) {
            case 'text':
                //Save the text answer directly
                $session->answer_text = $message['text']['body'];
                break;

            case 'image':
                //Save the image and caption
                $imageId = $message['image']['id'] ?? null;
                $imagePath = $this->mediaService->getMediaAndSave($imageId);
                $session->answer_text = $message['image']['caption'] ?? '';
                $session->answer_url = $imagePath;
                $session->answer_id = $imageId;
                $session->answer_type = AnswerType::Image;
                break;

            case 'video':
                //Save the video and caption
                $videoId = $message['video']['id'] ?? null;
                $videoPath = $this->mediaService->getMediaAndSave($videoId, 'answers');
                $session->answer_text = $message['video']['caption'] ?? '';
                $session->answer_url = $videoPath;
                $session->answer_id = $videoId;
                $session->answer_type = AnswerType::Video;
                break;

            case 'document':
                //Save the document and caption
                $docId = $message['document']['id'] ?? null;
                $docPath = $this->mediaService->getMediaAndSave($docId, 'answers');
                $session->answer_text = $message['document']['caption'] ?? '';
                $session->answer_document_url = $docPath;
                $session->answer_id = $docId;
                $session->answer_type = AnswerType::Document;
                break;

            default:
                // Unsupported message type
                $this->whatsAppService->sendText($from, "غير مدعومة. الرجاء إرسال نص، صورة، فيديو أو ملف PDF فقط.");
                return;
        }

        // Save the updated session data
        $session->save();

        // Reset the tutor's status back to default
        $tutor->update(['status' => UserState::Init]);

        // Ask tutor to confirm before finalizing the answer
        $this->AskForConfirmAnswer($from, $session->question_id);
    }



    /**
     * Ask the tutor to confirm before sending the answer to the student.
     *
     * @param string $to The tutor's WhatsApp number
     * @param int $questionId The ID of the question being answered
     * @return void
     */
    public function AskForConfirmAnswer($to, $questionId): void
    {
        $message = "*هل أنت متأكد من أن هذه هي الإجابة التي تريد إرسالها إلى الطالب؟*\n\n"
            . "يرجى تأكيد إرسال الإجابة ";

        $this->whatsAppService->sendButtons($to, $message, [
            [
                'type' => 'reply',
                'reply' => [
                    'id' => "confirm_answer_{$questionId}",
                    'title' => ' تأكيد الإرسال'
                ]
            ],
            [
                'type' => 'reply',
                'reply' => [
                    'id' => "cancel_answer_{$questionId}",
                    'title' => ' إلغاء '
                ]
            ]
        ]);
    }

    /**
     *  send the tutor's answer to the student after confirmation.
     *
     * @param string $from The tutor's WhatsApp number
     * @param string $questionId The ID of the question being answered
     * @return void
     */
    public function confirmAndSendAnswer(string $from, string $questionId): void
    {
        //Retrieve the question 
        $question = Question::with('student')->find($questionId);

        //Retrieve the tutor's session where the answer was stored temporarily
        $session = TutorQuestionSessions::where('whatsapp_number', $from)->first();

        //Copy the answer data from session to the actual question record
        $question->answer_text =  $session->answer_text;
        $question->answer_url = $session->answer_url;
        $question->answer_id = $session->answer_id;
        $question->answer_type = $session->answer_type;

        //Save the updated question
        $question->save();

        //Delete the session after confirmation
        $session->delete();

        //Send the answer to the student
        $to = $question->student->number;
        $this->sendAnswerTOStudent($to, $question);

        // Notify the tutor
        $this->whatsAppService->sendText($from, "تم إرسال الإجابة إلى الطالب بنجاح.");

        $this->addCommentOrRate($to, $questionId);
        $this->tutorActivities($question);
    }

    /**
     * Send the answer  to the student 
     *
     * @param string $to The student's WhatsApp number
     * @param Question $question The question with the answer data
     * @return void
     */
    public function sendAnswerTOStudent($to, Question $question): void
    {
        //Notify the student that an answer has arrived
        $this->whatsAppService->sendText($to, "لقد وصلتك إجابة على سؤالك رقم #{$question->id}");

        //Send text part of the answer, if available
        if ($question->answer_text) {
            $this->whatsAppService->sendText($to, $question->answer_text);
        }

        //Send image file if it's an image-type answer
        if ($question->answer_type === AnswerType::Image->value) {
            $this->whatsAppService->sendImage($to, asset($question->answer_url), "صورة الإجابة");
        }

        //Send video file if it's a video-type answer
        if ($question->answer_type === AnswerType::Video->value) {
            $this->whatsAppService->sendVideo($to, asset($question->answer_url), "فيديو الإجابة");
        }

        //Send document file if it's a document-type answer
        if ($question->answer_type === AnswerType::Document->value) {
            $this->whatsAppService->sendDocument($to, asset($question->answer_url), "ملف الإجابة");
        }
    }

    /**
     * Prompt the student to add a comment if the answer is unclear or incorrect.
     *
     * @param string $to The student's WhatsApp number
     * @param string $questionId The ID of the related question
     * @return void
     */
    public function addCommentOrRate(string $to, string $questionId): void
    {
        $message = "*يمكنك إضافة تعليق للمدرس حول السؤال إذا كان يحتاج إلى تعديل أو غير صحيح.*\n";

        $this->whatsAppService->sendButtons($to, $message, [
            [
                'type' => 'reply',
                'reply' => [
                    'id' => "add_comment_{$questionId}",
                    'title' => 'إضافة تعليق'
                ]
            ],
        ]);
    }

    /**
     * Cancel the tutor's current answer session and delete the draft.
     *
     * @param string $from The tutor's WhatsApp number
     * @return void
     */
    public function cancelAnswer(string $from): void
    {
        $session = TutorQuestionSessions::where('whatsapp_number', $from)->first();

        //Delete the session to discard the unsent answer
        $session?->delete();

        //Notify the tutor that the answer has been canceled
        $this->whatsAppService->sendText($from, "تم إلغاء الإجابة.");
    }


    public function tutorActivities(Question $question)
    {
        $existingActivity = TutorActivity::where('tutor_id', $question->tutor_id)
            ->where('question_id', $question->id)
            ->where('action', TutorActivities::Answer)
            ->first();
        TutorActivity::create([
            'tutor_id' => $question->tutor_id,
            'question_id' => $question->id,
            'action' => !empty($existingActivity) ? TutorActivities::Edit : TutorActivities::Answer,
        ]);
    }
}
