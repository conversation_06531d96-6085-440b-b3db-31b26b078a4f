<?php

namespace App\Filament\Settings\Forms;

use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Placeholder;
use Illuminate\Support\HtmlString;

class CatalogTemplate
{
    public static function getTab(): Tab
    {
        return Tab::make('catalog-template')
            ->label(__('filament-panels.setting.catalog'))
            ->icon('heroicon-o-document-arrow-down')
            ->schema(self::getFields())
            ->statePath('catalog_template')
            ->visible(true);
    }

    public static function getFields(): array
    {
        $xlsxUrl = route('meta.catalog.template', ['format' => 'xlsx']);
        $csvUrl = route('meta.catalog.template', ['format' => 'csv']);

        return [
            Placeholder::make('')
                ->content(__('filament-panels.setting.templateInfo')),

            Placeholder::make('')
                ->content(new HtmlString("
                    <div class='flex items-center gap-4 mt-2'>
                        <a href='{$xlsxUrl}' 
                           target='_blank' 
                           class='inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-450'>
                             Excel (.xlsx)
                        </a>
                        <a href='{$csvUrl}' 
                           target='_blank' 
                           class='inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-450'>
                             CSV (.csv)
                        </a>
                    </div>
                ")),
        ];
    }


    public static function getSortOrder(): int
    {
        return 10;
    }
}
