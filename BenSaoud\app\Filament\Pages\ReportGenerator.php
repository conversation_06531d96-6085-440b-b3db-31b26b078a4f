<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class ReportGenerator extends Page
{
    // Define the navigation icon for this page in the Filament sidebar
    protected static ?string $navigationIcon = 'heroicon-o-document';

    // Define the navigation group (optional, helps organize sidebar)

    // Define the route for this page.
    protected static string $view = 'filament.pages.report-generator';
    protected static ?int $navigationSort = 4; 

    public function getTitle(): string
    {
        return __('filament-panels.report.title');
    }
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.report.title');
    }
   

   
}
