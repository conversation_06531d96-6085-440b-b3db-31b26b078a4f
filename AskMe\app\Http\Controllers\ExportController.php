<?php

namespace App\Http\Controllers;

use App\Enums\TransactionStatus;
use App\Enums\TutorActivities;
use App\Models\Subscription;
use App\Models\SubscriptionPackage;
use App\Models\Transaction;
use App\Models\Tutor;
use App\Models\TutorActivity;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Mpdf\Mpdf;

class ExportController extends Controller
{
 
    public function generate(string $type, string $range, ?string $month = null)
    {
        switch ($type) {
            case 'tutors':
                return $this->generateTutorsReport($range, $month);

            case 'revenue':
                return $this->generateRevenueReport($range, $month);

            case 'subscriptions':
                return $this->generateSubscriptionsReport($range, $month);

                abort(404);
        }
    }

    private function generateTutorsReport(string $range, ?string $month = null)
    {
        $query = Tutor::with('user')
            ->withCount(['questions as answered_count' => function ($q) use ($range, $month) {
                $q->whereNotNull('answer_text');

                if ($range === 'month' && $month) {
                    $q->whereMonth('questions.created_at', $month);
                }
                if ($range === 'year') {
                    $q->whereYear('questions.created_at', now()->year);
                }
                if ($range === 'week') {
                    $q->whereBetween('questions.created_at', [
                        now()->startOfWeek(),
                        now()->endOfWeek()
                    ]);
                }
            }]);

        $tutors = $query->get()
            ->map(function ($tutor) use ($range, $month) {
                $activityQuery = TutorActivity::query()
                    ->where('action', TutorActivities::Answer)
                    ->where('tutor_activities.tutor_id', $tutor->id)
                    ->join('questions', 'tutor_activities.question_id', '=', 'questions.id');

                if ($range === 'month' && $month) {
                    $activityQuery->whereMonth('questions.created_at', $month);
                }
                if ($range === 'year') {
                    $activityQuery->whereYear('questions.created_at', now()->year);
                }
                if ($range === 'week') {
                    $activityQuery->whereBetween('questions.created_at', [
                        now()->startOfWeek(),
                        now()->endOfWeek()
                    ]);
                }

                $avgSeconds = $activityQuery->avg(DB::raw('TIMESTAMPDIFF(SECOND, questions.created_at, tutor_activities.created_at)'));
                $tutor->avg_answer_time = $avgSeconds ? round($avgSeconds / 60, 1) : null;

                return $tutor;
            });

        $html = view('exports.tutors', compact('tutors', 'range', 'month'))->render();

        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font' => 'sans',
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);

        $mpdf->WriteHTML($html);

        return response($mpdf->Output('tutor-report.pdf', 'I'), 200)
            ->header('Content-Type', 'application/pdf');
    }

    private function generateRevenueReport(string $range, ?string $month = null)
    {
        $query = Transaction::query()
            ->where('status', TransactionStatus::COMPLETED);

        if ($range === 'month' && $month) {
            $query->whereMonth('created_at', $month);
        }
        if ($range === 'year') {
            $query->whereYear('created_at', now()->year);
        }
        if ($range === 'week') {
            $query->whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ]);
        }

        $totalRevenue = $query->sum('amount');
        $transactionsCount = $query->count();

        // نحضر البيانات يوم بيوم
        $dailyRevenues = $query->selectRaw('DATE(created_at) as date, COUNT(*) as transactions_count, SUM(amount) as total_amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // تجهيز بيانات للعرض
        $html = view('exports.revenue', [
            'totalRevenue' => $totalRevenue,
            'transactionsCount' => $transactionsCount,
            'dailyRevenues' => $dailyRevenues,
            'range' => $range,
            'month' => $month,
            'weekStart' => now()->startOfWeek()->format('d/m/Y'),
            'weekEnd' => now()->endOfWeek()->format('d/m/Y'),
        ])->render();

        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font' => 'sans',
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);

        $mpdf->WriteHTML($html);

        return response($mpdf->Output('revenue-report.pdf', 'I'), 200)
            ->header('Content-Type', 'application/pdf');
    }

    private function generateSubscriptionsReport(string $range, ?string $month = null)
    {
        $query = Subscription::query();
            // ->where('status', 'active');

        if ($range === 'month' && $month) {
            $query->whereMonth('created_at', $month);
        }
        if ($range === 'year') {
            $query->whereYear('created_at', now()->year);
        }
        if ($range === 'week') {
            $query->whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ]);
        }

        $totalSubscriptions = $query->count();

        $dailySubscriptions = $query->selectRaw('DATE(created_at) as date, COUNT(*) as subscriptions_count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $packageSubscriptions = $query->selectRaw('subscription_packages_id, COUNT(*) as subscriptions_count')
            ->groupBy('subscription_packages_id')
            ->with('package')
            ->get();

        $html = view('exports.subscriptions', [
            'totalSubscriptions' => $totalSubscriptions,
            'dailySubscriptions' => $dailySubscriptions,
            'packageSubscriptions' => $packageSubscriptions,
            'range' => $range,
            'month' => $month,
            'weekStart' => now()->startOfWeek()->format('d/m/Y'),
            'weekEnd' => now()->endOfWeek()->format('d/m/Y'),
        ])->render();

        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font' => 'sans',
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);

        $mpdf->WriteHTML($html);

        return response($mpdf->Output('subscriptions-report.pdf', 'I'), 200)
            ->header('Content-Type', 'application/pdf');
    }
}
