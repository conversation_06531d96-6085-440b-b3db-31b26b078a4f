<?php

namespace App\Filament\Tutor\Pages;

use App\Enums\TutorActivities;
use App\Models\Question;
use App\Models\Tutor;
use App\Models\TutorActivity;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class ViewAndAcceptQuestion extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.view-and-accept-question';
    protected static bool $shouldRegisterNavigation = false;

    public ?Question $question = null;
    public function getTitle(): string
    {
        return __('filament-panels.question_request.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.question_request.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.question_request.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.question_request.title');
    }
    public static function getSlug(): string
    {
        return 'view-and-accept-question/{record}';
    }

    public function mount(Question $record): void
    {
        $this->question =   $record;
    }

    public function accept(): void
    {
        if ($this->question->tutor_id !== null) {
            Notification::make()
                ->title('This question has already been accepted.')
                ->danger()
                ->send();
            return;
        }

        $tutorId = Tutor::where('user_id', Auth::id())->value('id');
        $this->question->update(['tutor_id' => $tutorId]);
        TutorActivity::create([
            'tutor_id' => $tutorId,
            'question_id' => $this->question->id,
            'activity_type' => TutorActivities::Accept,
        ]);

        Notification::make()
            ->title(__('filament-panels.question.notification.question_accepted'))
            ->success()
            ->send();

        $this->redirect('/tutor/answer-question/' . $this->question->id);
    }
}
