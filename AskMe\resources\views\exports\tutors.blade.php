<!DOCTYPE html>
<html lang="ar">

<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            direction: rtl;
            text-align: right;
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: center;
        }

        th {
            background-color: #eee;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .divider {
            border: none;
            border-top: 2px solid #333;
            margin: 20px 0;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>أدرس للتعليم الإلكتروني</h1>
        <h3>خدمة اسألني</h3>

        <p style="font-size: 18px; font-weight: bold; margin-top: 10px;">
            تقرير المعلمين
        </p>

        {{-- الفترة الزمنية --}}
        <p style="margin-top: 5px;">
            @if ($range == 'month' && $month)
            لشهر {{ $month}}
            @elseif ($range == 'year')
            للسنة الحالية: {{ now()->format('Y') }}
            @elseif ($range == 'week')
            للأسبوع من {{ $weekStart }} إلى {{ $weekEnd }}
            @else
            للفترة المحددة
            @endif
        </p>

        {{-- وصف عام للتقرير --}}
        <p style="margin-top: 10px; font-size: 14px; color: #666;">
            يحتوي هذا التقرير على ترتيب المعلمين بناءً على عدد الأسئلة المجابة ومتوسط وقت الإجابة خلال الفترة الزمنية المختارة.
        </p>
    </div>



    <hr class="divider">

    <table>
        <thead>
            <tr>
                <th>الترتيب</th>
                <th>المعلم</th>
                <th>الأسئلة المجابة</th>
                <th>متوسط وقت الإجابة</th>
            </tr>
        </thead>
        <tbody>
            @foreach($tutors as $i => $tutor)
            <tr>
                <td>{{ $i + 1 }}</td>
                <td>{{ $tutor->user->name ?? '—' }}</td>
                <td>{{ $tutor->answered_count }}</td>
                <td>{{ $tutor->avg_answer_time ? $tutor->avg_answer_time . ' دقيقة' : '—' }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
</body>

</html>