<?php

namespace App\Filament\Widgets;

use App\Models\Products; 
use Filament\Tables\Columns\TextColumn; 
use Filament\Tables\Table; 
use Filament\Widgets\TableWidget as BaseWidget; 
use Illuminate\Support\Facades\DB;

/**
 * Class TopSellingProducts
 *
 * This Filament Table Widget displays a list of the top 10 best-selling products.
 * It calculates total quantity sold and total revenue for each product.
 */
class TopSellingProducts extends BaseWidget
{
    /**
     * @var int|null The sort order for this widget in the Filament dashboard.
     * Lower numbers appear earlier.
     */
    protected static ?int $sort = 4;

    /**
     * Get the heading (title) for the table widget.
     *
     * @return string The translated heading for the table.
     */
    public function getHeading(): string
    {
        return __('filament-panels.chart.top_selling_products');
    }

    /**
     * Configures the table for the widget.
     *
     * @param Table $table The Filament Table instance to configure.
     * @return Table The configured Table instance.
     */
    public function table(Table $table): Table
    {
        return $table
            ->query(
                Products::query()
                    ->withSum('orderItems as total_quantity', 'quantity')
                    ->withSum('orderItems as total_revenue', DB::raw('quantity * price'))
                    ->orderByDesc('total_quantity')
                    ->limit(10)
            )
            ->columns([
                TextColumn::make('name')->label(__('filament-panels.products.fields.name')),
                TextColumn::make('total_quantity')
                    ->label(__('filament-panels.products.fields.quantity'))
                    ->formatStateUsing(fn ($state, $record) => $record->total_quantity ?? 0),
                TextColumn::make('total_revenue')
                    ->label(__('filament-panels.products.fields.revenues'))
                    ->formatStateUsing(fn ($state) => number_format($state, 2) . ' د.ل')
                    ->sortable(),
            ]);
    }
}