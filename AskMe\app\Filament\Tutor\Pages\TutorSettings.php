<?php

namespace App\Filament\Tutor\Pages;

use App\Models\Grade;
use App\Models\Subject;
use App\Models\Tutor;
use App\Models\TutorSubject;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class TutorSettings extends Page
{
    protected static ?string $navigationIcon = 'heroicon-c-cog-8-tooth';

    protected static string $view = 'filament.pages.tutor-settings';
    protected static ?string $title = '';
    protected static bool $shouldRegisterNavigation = true;
    public array $data = [];


    public function getTitle(): string
    {
        return __('filament-panels.setting.plural');
    }
    public static function getModelLabel(): string
    {
        return __('filament-panels.setting.singular');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.setting.plural');
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-panels.setting.title');
    }

    public function mount(): void
    {
        $is_active = Auth::user()->is_active;
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');
        $tutor = Tutor::where("id", $tutorId)->with('tutorSubjects')->first();
        $this->data = [
            'tutorSubjects' => $tutor->tutorSubjects->map(fn($ts) => [
                'subject_id' => $ts->subject_id,
                'grade_id' => $ts->grade_id,
            ])->toArray(),
            'is_active' => $tutor->user->is_active === 1 ? true : false,
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                Fieldset::make(__('filament-panels.setting.subjects'))
                    ->schema([
                        Repeater::make('tutorSubjects')
                        ->label(__('filament-panels.setting.subjects'))
                            ->schema([
                                Select::make('subject_id')
                                    ->label(__('filament-panels.subject.singular'))
                                    ->options(Subject::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),


                                Select::make('grade_id')
                                    ->label(__('filament-panels.grade.singular'))
                                    ->options(Grade::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                            ])
                    ]),

                Checkbox::make('is_active')
                    ->label(__('filament-panels.setting.active')),

                Fieldset::make(__('filament-panels.setting.password'))
                    ->schema([
                        TextInput::make('password')
                            ->label(__('filament-panels.setting.password'))
                            ->password()
                            ->minLength(6)
                            ->maxLength(50)
                            ->confirmed()
                            ->nullable(),

                        TextInput::make('password_confirmation')
                            ->label(__('filament-panels.setting.password_confirm'))
                            ->password()
                            ->same('password')
                            ->nullable(),
                    ]),
            ]);
    }

    public function submit(): void
    {
        $user = Auth::user();
        $tutor = Tutor::where('user_id', $user->id)
            ->with('tutorSubjects')
            ->with('user')
            ->firstOrFail();

        $data = $this->form->getState();

        // Update activation status
        $tutor->user->is_active = $data['is_active'] ?? false;

        // Only update password if it's filled
        if (!empty($data['password'])) {
            $tutor->user->password = Hash::make($data['password']);
        }

        $tutor->user->save();

        // Only update grades/subjects if changed
        $newGradeIds = $data['grade_ids'] ?? [];
        $newSubjectIds = $data['subject_ids'] ?? [];

        $existing = $tutor->tutorSubjects->map(function ($item) {
            return $item->grade_id . '-' . $item->subject_id;
        })->toArray();
        // ✅ Process tutorSubjects from repeater
        $newCombinations = collect($data['tutorSubjects'] ?? [])
            ->map(fn($row) => $row['grade_id'] . '-' . $row['subject_id'])
            ->toArray();

        $existingCombinations = $tutor->tutorSubjects->map(function ($item) {
            return $item->grade_id . '-' . $item->subject_id;
        })->toArray();

        if (array_diff($newCombinations, $existingCombinations) || array_diff($existingCombinations, $newCombinations)) {
            TutorSubject::where('tutor_id', $tutor->id)->delete();

            foreach ($data['tutorSubjects'] ?? [] as $row) {
                TutorSubject::create([
                    'tutor_id' => $tutor->id,
                    'grade_id' => $row['grade_id'],
                    'subject_id' => $row['subject_id'],
                ]);
            }
        }
        Notification::make()
            ->title(__('filament-panels.setting.seved'))
            ->success()
            ->send();
    }
}
