<?php

namespace App\Filament\Admin\Widgets;

use App\Models\SubscriptionPackage;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class PackagePerformanceTable extends BaseWidget
{
    protected static ?string $heading = 'تحليل أداء الباقات';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return$table
        ->query(
            SubscriptionPackage::query()->withCount('subscriptions')
        )
        ->columns([
            TextColumn::make('name')
                ->label(__('filament-panels.dashboard.package_name'))
                ->sortable(),

            TextColumn::make('price')
                ->label(__('filament-panels.dashboard.package_price'))
                ->formatStateUsing(fn ($state) => number_format($state, 2) . ' د.ل'),

            TextColumn::make('subscriptions_count')
                ->label(__('filament-panels.dashboard.package_sold'))
                ->badge()
                ->color('primary'),

            TextColumn::make('total_revenue')
                ->label(__('filament-panels.dashboard.total_revenue'))
                ->state(fn ($record) => $record->price * $record->subscriptions_count)
                ->formatStateUsing(fn ($state) => number_format($state, 2) . ' د.ل')
                ->badge()
                ->color('success'),
        ]);
    }
}
