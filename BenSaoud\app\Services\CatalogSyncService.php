<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use App\Models\Products;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CatalogSyncService
{

    /**
     * Sends the product data to the Meta Catalog API .
     *
     * @param Products $product The product data 
     * @return Response The HTTP response from Meta API
     */
    public function createProduct(Products $product)
    {
        $payload = $this->buildPayload($product);

        $url = config('api.whatsapp_api_url') . '661190839776792/products';
        Log::info("Sending product to Meta Catalog", [
            'url' => $url,
            'payload' => $payload,
        ]);
        return Http::withToken(config('api.whatsapp_access_token'))
            ->post($url, $payload);
    }

    public function updateProduct(Products $product, $changes, $productId)
    {
        $payload = $this->buildUpdatePayload($product, $changes);
        $url = config('api.whatsapp_api_url') . $productId;

        Log::info("Sending product to Meta Catalog", [
            'url' => $url,
            'payload' => $payload,
        ]);

        return Http::withToken(config('api.whatsapp_access_token'))
            ->post($url, $payload);
    }

    public function updateDiscountProduct(Products $product, array $payload = [])
    {
        $payload = empty($payload) ? $this->buildDiscountPayload($product) : $payload;
        $url = config('api.whatsapp_api_url') . $product->product_catelog_id;

        Log::info("Sending product to Meta Catalog", [
            'url' => $url,
            'payload' => $payload,
        ]);
        return Http::withToken(config('api.whatsapp_access_token'))
            ->post($url, $payload);
    }
    public function deleteProduct(int $productId): void
    {
        $url = config('api.whatsapp_api_url') . $productId;
        $response =  Http::withToken(config('api.whatsapp_access_token'))
            ->withoutVerifying()

            ->delete($url);
        $responseJson = $response->json();
        Log::info("Catelog Delete response: ", [$responseJson]);
    }


    /**
     * Builds the payload to be sent to the Facebook Catalog API.
     * Filters out null values and formats the fields as expected.
     *
     * @param Products $product The product model
     * @return array The cleaned payload
     */
    protected function buildPayload(Products $product): array
    {
        $mainImage = $product->images()->first();

        $additionalImages = $product->images()
            ->pluck('image_url')
            ->slice(1)
            ->take(9)
            ->toArray();

        return array_filter([
            'name' => $product->name,
            'retailer_id' => empty($product->retailer_id) ? $product->id : $product->retailer_id,
            'description' => $product->desc,
            'image_url' => Str::startsWith($mainImage->image_url, ['http://', 'https://']) ? $mainImage->image_url : asset('storage/' . $mainImage->image_url),
            "additional_image_urls" => array_values(
                array_map(
                    fn($path) => Str::startsWith($path, ['http://', 'https://'])
                        ? $path
                        : asset('storage/' . $path),
                    $additionalImages ?? []
                )
            ),
            'price' => (int)$product->price * 100,
            'brand' => $product->brand,
            'currency' => 'LYD',
            'url' => $product->website_link
        ], fn($value) => !is_null($value));
    }

    protected function buildUpdatePayload(Products $record, $changes): array
    {
        $metaFields = [
            'name'        => 'name',
            'desc'        => 'description',
            'price'       => 'price',
            'brand'       => 'brand',
            'website_link' => 'url',
        ];
        $payload = [];
        foreach ($metaFields as $modelField => $metaKey) {
            if (array_key_exists($modelField, $changes)) {
                $payload[$metaKey] = $record->$modelField;
            }
        }

        return $payload;
    }

    protected function buildDiscountPayload(Products $record): array
    {
        $discount = $record->getActiveDiscount();
        if ($discount) {
            $discountedPrice =  max($record->price - ($record->price * $discount->value / 100), 0);
            $payload['sale_price'] = (int) round($discountedPrice *  100, 0);
        }
        return $payload;
    }
}
