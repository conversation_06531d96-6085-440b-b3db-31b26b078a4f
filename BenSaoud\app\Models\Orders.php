<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Orders extends Model
{
    use HasFactory;
    
    protected $table = 'orders';
    protected $fillable = ['customer_id', 'payment_method', 'total_price'];

    public function customer() { return $this->belongsTo(Customer::class); }
    
    
    public function orderItems() { return $this->hasMany(OrderItems::class, 'order_id'); }

}