<?php

namespace App\Filament\Pages;

use App\Filament\Settings\Forms\AppSettingsTab;
use App\Filament\Settings\Forms\CatalogTemplate;
use App\Models\User;
use Closure;
use Filament\Forms\Components\Tabs;
use Illuminate\Support\Facades\Auth;
use Outerweb\FilamentSettings\Filament\Pages\Settings as BaseSettings;
use Outerweb\Settings\Models\Setting;

class Settings extends BaseSettings
{

    protected static ?int $navigationSort = 5; 
    /**
     * Defines the form schema for the settings page.
     *
     * @return array|Closure
     */
    public function schema(): array|Closure
    {
        return [
            Tabs::make('Settings') // Create a tab component named 'Settings'
                ->schema([
                    // Include settings tabs defined in separate form classes
                    AppSettingsTab::getTab(), 
                    CatalogTemplate::getTab(), 
                ]),
        ];
    }

    /**
     * This method is called after the settings form is successfully saved.
     * It's used here to handle a specific logic: if a 'general.password' setting exists,
     * it updates the authenticated user's password and then deletes the setting.
     *
     * @return void
     */
    protected function afterSave(): void
    {
        // Get the currently authenticated user
        $user = Auth::user();

        // Retrieve the 'general.password' setting value
        $password = Setting::get('general.password');

        // Check if the password setting exists and has a value
        if ($password) {
            // Update the authenticated user's password in the database
            User::where('id', $user->id)->update([
                'password' => $password,
            ]);

            // Delete the 'general.password' setting after it has been used
            Setting::where('key', 'general.password')->delete();
        }
    }

    /**
     * Get the singular label for the model associated with this page.
     * Used for display purposes in Filament.
     *
     * @return string
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.setting.singular');
    }

    /**
     * Get the plural label for the model associated with this page.
     * Used for display purposes in Filament.
     *
     * @return string
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.setting.plural');
    }

    /**
     * Get the navigation label for this page in the Filament sidebar.
     *
     * @return string
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.setting.title');
    }

    /**
     * Get the title of the settings page.
     *
     * @return string
     */
    public function getTitle(): string
    {
        return __('الإعدادات العامة'); // "General Settings" in Arabic
    }
}
