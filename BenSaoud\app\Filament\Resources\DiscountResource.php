<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DiscountProductsResource\RelationManagers\ProductsRelationManager;
use App\Filament\Resources\DiscountResource\Pages;
use App\Filament\Resources\DiscountResource\RelationManagers;
use App\Models\Discount;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class DiscountResource extends Resource
{
    // Defines the Eloquent model that this Filament resource manages.
    protected static ?string $model = Discount::class;

    // Sets the icon that will be displayed next to the resource in the Filament sidebar navigation.
    protected static ?string $navigationIcon = 'heroicon-m-tag';

    /**
     * Defines the form schema for creating and editing Discount records.
     *
     * @param Form $form
     * @return Form
     */
    public static function form(Form $form): Form
    {
        return $form->schema([
            // Text input for the discount name.
            TextInput::make('name')
                ->label(__('filament-panels.discount.fields.name')), 

            // Numeric input for the discount value.
            TextInput::make('value')
                ->numeric() // Ensures only numeric input
                ->required() // Makes the field mandatory
                ->minValue(1) // Sets a minimum value of 1
                ->label(__('filament-panels.discount.fields.value')), 

            // Date picker for the discount start date.
            DatePicker::make('start_at')
                ->label(__('filament-panels.discount.fields.start')) 
                ->required(), // Makes the field mandatory

            // Date picker for the discount end date.
            DatePicker::make('end_at')
                ->label(__('filament-panels.discount.fields.end')) 
                ->required(), // Makes the field mandatory
        ]);
    }

    /**
     * Defines the table schema for listing Discount records.
     *
     * @param Table $table
     * @return Table
     */
    public static function table(Table $table): Table
    {
        return $table->columns([
            // Text column to display the discount name.
            TextColumn::make('name')
                ->label(__('filament-panels.discount.fields.name')), 

            // Text column to display the discount value.
            TextColumn::make('value')
                ->label('Value') 
                ->label(__('filament-panels.discount.fields.value')), 

            // Text column to display the discount start date.
            TextColumn::make('start_at')
                ->label(__('filament-panels.discount.fields.start')) 
                ->date(), 

            // Text column to display the discount end date.
            TextColumn::make('end_at')
                ->label(__('filament-panels.discount.fields.end')) 
                ->date(), // Formats the date
        ])
            ->filters([
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * Get the singular label for the model associated with this resource.
     *
     * @return string
     */
    public static function getModelLabel(): string
    {
        // Translate the singular discount label using Laravel's translation helper
        return __('filament-panels.discount.singular');
    }

    /**
     * Get the plural label for the model associated with this resource.
     *
     * @return string
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.discount.plural');
    }

    /**
     * Get the navigation label for this resource in the Filament sidebar.
     *
     * @return string
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.discount.title');
    }

    /**
     * Defines the relationships that can be managed directly from this resource.
     *
     * @return array
     */
    public static function getRelations(): array
    {
        return [
            ProductsRelationManager::class
        ];
    }

    /**
     * Defines the pages associated with this resource.
     *
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDiscounts::route('/'), 
            'create' => Pages\CreateDiscount::route('/create'), 
            'edit' => Pages\EditDiscount::route('/{record}/edit'), 
        ];
    }
}
