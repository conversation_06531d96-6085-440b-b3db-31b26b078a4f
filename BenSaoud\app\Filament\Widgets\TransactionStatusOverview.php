<?php

namespace App\Filament\Widgets;

use App\Enums\TransactionStatus; 
use App\Models\WhatsappTransactions; 
use Filament\Widgets\StatsOverviewWidget as BaseWidget; 
use Filament\Widgets\StatsOverviewWidget\Stat; 

/**
 * Class TransactionStatusOverview
 *
 * This Filament Stats Overview Widget displays key statistics related to WhatsApp transactions.
 * It provides a summary of total transaction amount, total number of transactions,
 * and counts for transactions in pending, completed, failed, and canceled statuses.
 */
class TransactionStatusOverview extends BaseWidget
{
    /**
     * @var string|null A static property to set a unique name for the widget.
     * This can be useful for referencing the widget programmatically.
     */
    protected static ?string $name = 'transactionsStatusOverview';

    /**
     * Determines whether the widget can be viewed.
     * This method controls the visibility of the widget based on the current route.
     *
     * @return bool True if the widget should be visible, false otherwise.
     */
    public static function canView(): bool
    {
        return str(request()->route()?->getName())->contains('transactions');
    }

    /**
     * Defines the statistics to be displayed in the widget.
     *
     * @return array An array of Stat objects, each representing a single statistic.
     */
    protected function getStats(): array
    {
        return [
            // Stat for Total Transaction Amount:
            Stat::make(
                __('filament-panels.transactions.totalAmount'),
                WhatsappTransactions::sum("amount") 
            ),

            // Stat for Total Number of Transactions:
            Stat::make(
                __('filament-panels.transactions.TotalTransactions'), 
                WhatsappTransactions::count() 
            ),

            // Stat for Pending Transactions:
            Stat::make(
                __('filament-panels.transactions.pending'), 
                WhatsappTransactions::where('status', TransactionStatus::PENDING)->count() 
            )
                ->color('warning'), 

            // Stat for Paid (Completed) Transactions:
            Stat::make(
                __('filament-panels.transactions.paid'), 
                WhatsappTransactions::where('status', TransactionStatus::COMPLETED)->count() 
            )
                ->color('success'), 

            // Stat for Failed Transactions:
            Stat::make(
                __('filament-panels.transactions.failed'), 
                WhatsappTransactions::where('status', TransactionStatus::FAILED)->count() 
            )
                ->color('danger'), 

            // Stat for Cancelled Transactions:
            Stat::make(
                __('filament-panels.transactions.cancelled'), 
                WhatsappTransactions::where('status', TransactionStatus::CANCELED)->count() 
            )
                ->color('gray'), 
        ];
    }
}