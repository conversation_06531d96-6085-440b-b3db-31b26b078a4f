<?php

namespace App\Filament\Admin\Widgets;

use App\Models\SubscriptionPackage;
use Filament\Widgets\ChartWidget;

class SubscriptionPackagePieChart extends ChartWidget
{
    protected static ?string $heading = 'نسبة مبيعات كل باقة اشتراك';
    protected static ?int $sort = 4;
    // Max chart height
    protected static ?string $maxHeight = '300px';

    protected function getData(): array
    {
        $packages = SubscriptionPackage::withCount('subscriptions')->get();

        return [
            'datasets' => [
                [
                    'label' => __('filament-panels.dashboard.packages_sold'),
                    'data' => $packages->pluck('subscriptions_count'),
                    'backgroundColor' => [
                        '#3b82f6', // blue
                        '#f97316', // orange
                        '#10b981', // green
                        '#f43f5e', // pink
                        '#a855f7', // purple
                    ],
                ],
            ],
            'labels' => $packages->pluck('name'),
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
