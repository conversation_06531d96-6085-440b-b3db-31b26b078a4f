<?php

namespace App\Filament\Resources\ProductsResource\RelationManagers;

use App\Models\Products;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\AttachAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

/**
 * ProductsRelationManager
 * 
 * Manages the relationship between categories and products in the Filament admin panel.
 * Allows attaching and detaching products to/from categories, with the ability to
 * add new products or select existing ones.
 */
class ProductsRelationManager extends RelationManager
{
    protected static string $relationship = 'products';

    /**
     * Define the form fields for creating new products
     * Fields include product name and price with Arabic labels
     */
    public function form(Form $form): Form
    {
        return $form->schema([
            // Product name field 
            TextInput::make('name')->label(__('filament-panels.products.fields.name'))->required(),
            // Product price field 
            TextInput::make('price')->label(__('filament-panels.products.fields.price'))->numeric()->required(),
        ]);
    }

    /**
     * Define the table columns and actions for managing products
     * Includes product listing, attachment, and detachment capabilities
     */
    public function table(Table $table): Table
    {
        return $table
            ->columns([
                // Product name column with Arabic label
                Tables\Columns\TextColumn::make('name')->label(__('filament-panels.products.fields.name')),
                // Product price column with Arabic label
                Tables\Columns\TextColumn::make('price')->label(__('filament-panels.products.fields.price')),
            ])
            ->headerActions([
                // Action to attach existing products to the category
                AttachAction::make()
                ->label(__('filament-panels.categories.action.addProduct'))
                ->recordSelect(function ($livewire) {
                        // Get the current category
                        $category = $livewire->getOwnerRecord();

                        // Get IDs of already attached products
                        $attachedProductIds = $category->products()->pluck('products.id')->toArray();

                        // Create a select field for choosing products
                        return Select::make('recordId')
                            ->label(__('filament-panels.categories.action.selectProduct'))
                            ->options(
                                // Only show products that aren't already attached
                                Products::whereNotIn('id', $attachedProductIds)
                                    ->pluck('name', 'id')
                            )
                            ->searchable()
                            ->preload();
                    }),
            ])
            ->actions([
                Tables\Actions\DetachAction::make()
            ])
            ->bulkActions([
                // Bulk actions for detaching multiple products
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make(),
                ]),
            ]);
    }
}
