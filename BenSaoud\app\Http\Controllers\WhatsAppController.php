<?php

namespace App\Http\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

use App\Http\Controllers\Controller;
use App\Services\StoreWhatsAppService;

use App\Services\WhatsAppService;

class WhatsAppController extends Controller
{

    protected $storeWhatsAppService;

    public function __construct(
        StoreWhatsAppService $storeWhatsAppService,
    ) {
        $this->storeWhatsAppService = $storeWhatsAppService;
    }
    /**
     * Verify Webhook for Meta
     */
    public function verifyWebhook(Request $request)
    {
        $verifyToken = config('api.webhook_verify_token');
        if ($request->hub_mode === 'subscribe' && $request->hub_verify_token === $verifyToken) {
            return response($request->hub_challenge); // Return challenge to verify webhook
        }

        return response()->json(['error' => 'Invalid Token'], 403);
    }

    /**
     * Handles incoming webhook events from WhatsApp API.
     * 
     * 1. Retrieves and decodes the webhook payload.
     * 2. Validates the presence of required data .
     * 3. Routes the webhook based on phone_number_id using match expression:
     *    - 583055918233147 : WhatsApp Store logic
     *    - 610414468814592 : Subscription payment logic
     *
     * @param Request $request
     */

    public function handleWebhook(Request $request)
    {
        // Retrieve the webhook payload
        $data = $request->all();
        Log::info("WhatsApp Webhook Received: " . json_encode($data));

        $value = $data['entry'][0]['changes'][0]['value'] ?? null;
        $phoneNumberId = $value['metadata']['phone_number_id'] ?? null;
        if (!$value || !$phoneNumberId) {
            Log::info("error invalid payload");
            return;
        }

        return match ($phoneNumberId) {
           config('api.whatsapp_phone_id') => $this->storeWhatsAppService->whatsAppStoreWeebhook($value),
            default =>  Log::info("Unknown phone number")
        };
    }
}
